// Domain Entity for UniToTslsReportSubmission
export class UniToTslsReportSubmission {
  constructor(
    public readonly uniToTslsReportSubmissionId: string,
    public readonly uniToTslsReportSubmissionName: string,
    public readonly uniToTslsReportSubmissionFilePath: string,
    public readonly submittedBy: string,
    public readonly uniToTslsDropboxId: string,
    public readonly submittedAt: Date = new Date(),
    public readonly notes: string = ''
  ) {}

  // Domain methods
  public updateNotes(newNotes: string): UniToTslsReportSubmission {
    return new UniToTslsReportSubmission(
      this.uniToTslsReportSubmissionId,
      this.uniToTslsReportSubmissionName,
      this.uniToTslsReportSubmissionFilePath,
      this.submittedBy,
      this.uniToTslsDropboxId,
      this.submittedAt,
      newNotes
    );
  }

  // Business rules
  public isValidFilePath(): boolean {
    return this.uniToTslsReportSubmissionFilePath.trim().length > 0 &&
           (this.uniToTslsReportSubmissionFilePath.endsWith('.pdf') ||
            this.uniToTslsReportSubmissionFilePath.endsWith('.doc') ||
            this.uniToTslsReportSubmissionFilePath.endsWith('.docx') ||
            this.uniToTslsReportSubmissionFilePath.endsWith('.xlsx') ||
            this.uniToTslsReportSubmissionFilePath.endsWith('.xls'));
  }

  public isRecentSubmission(hoursThreshold: number = 24): boolean {
    const now = new Date();
    const diffInHours = (now.getTime() - this.submittedAt.getTime()) / (1000 * 60 * 60);
    return diffInHours <= hoursThreshold;
  }

  public getFileExtension(): string {
    const parts = this.uniToTslsReportSubmissionFilePath.split('.');
    return parts.length > 1 ? parts[parts.length - 1].toLowerCase() : '';
  }

  public getFileName(): string {
    const parts = this.uniToTslsReportSubmissionFilePath.split('/');
    return parts[parts.length - 1];
  }

  public hasNotes(): boolean {
    return this.notes.trim().length > 0;
  }

  public getFileSizeFromPath(): string {
    // This would typically integrate with file system to get actual size
    // For now, return a placeholder
    return 'Unknown';
  }
}
