// Department routes
import { FastifyInstance } from 'fastify';
import { DepartmentController } from '../controllers/DepartmentController.js';
import { CreateDepartmentDto, UpdateDepartmentDto } from '../../../application/dto/department.dto.js';
import { verifyJWT } from '../middleware/auth.js';

export default async function departmentRoutes(fastify: FastifyInstance) {
  const departmentController = new DepartmentController(fastify.prisma);

  // All routes require authentication
  fastify.addHook('preHandler', verifyJWT);

  // Get all departments
  fastify.get('/get-all-departments', departmentController.getAllDepartments.bind(departmentController));

  // Get department by ID
  fastify.get<{ Params: { id: string } }>('/get-department-by-id/:id', departmentController.getDepartmentById.bind(departmentController));

  // Create new department
  fastify.post<{ Body: CreateDepartmentDto }>('/create-new-department', departmentController.createDepartment.bind(departmentController));

  // Update department
  fastify.put<{ Params: { id: string }; Body: UpdateDepartmentDto }>('/update-department/:id', departmentController.updateDepartment.bind(departmentController));

  // Delete department
  fastify.delete<{ Params: { id: string } }>('/delete-department/:id', departmentController.deleteDepartment.bind(departmentController));
}
