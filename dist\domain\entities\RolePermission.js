// Domain Entity for RolePermission (Junction table)
export class RolePermission {
    roleId;
    permissionId;
    constructor(roleId, permissionId) {
        this.roleId = roleId;
        this.permissionId = permissionId;
    }
    // Domain methods
    static createRolePermissions(roleId, permissionIds) {
        return permissionIds.map(permissionId => new RolePermission(roleId, permissionId));
    }
    // Business rules
    equals(other) {
        return this.roleId === other.roleId && this.permissionId === other.permissionId;
    }
    getCompositeKey() {
        return `${this.roleId}-${this.permissionId}`;
    }
}
