// Domain Entity for UniversityUser (Junction table)
export class UniversityUser {
    universityUserId;
    userId;
    universityId;
    createdAt;
    isActive;
    constructor(universityUserId, userId, universityId, createdAt = new Date(), isActive = true) {
        this.universityUserId = universityUserId;
        this.userId = userId;
        this.universityId = universityId;
        this.createdAt = createdAt;
        this.isActive = isActive;
    }
    // Domain methods
    deactivate() {
        return new UniversityUser(this.universityUserId, this.userId, this.universityId, this.createdAt, false);
    }
    activate() {
        return new UniversityUser(this.universityUserId, this.userId, this.universityId, this.createdAt, true);
    }
    // Business rules
    isValidForOperations() {
        return this.isActive;
    }
    equals(other) {
        return this.userId === other.userId && this.universityId === other.universityId;
    }
    getCompositeKey() {
        return `${this.userId}-${this.universityId}`;
    }
}
