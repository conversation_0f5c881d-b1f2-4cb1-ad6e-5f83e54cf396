import jwt from 'jsonwebtoken';
import { env } from '../../../infrastructure/config/env.js';
export async function verifyJWT(request, reply) {
    try {
        const authHeader = request.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return reply.status(401).send({ error: 'Missing or invalid authorization header' });
        }
        const token = authHeader.substring(7);
        const decoded = jwt.verify(token, env.JWT_SECRET);
        // Add user info to request
        request.user = {
            userId: decoded.sub,
            email: decoded.email,
            role: decoded.role,
            userType: decoded.userType
        };
    }
    catch (error) {
        return reply.status(401).send({ error: 'Invalid token' });
    }
}
