// Authentication DTOs
export interface LoginDto {
  email: string;
  password: string;
}

export interface LoginResponseDto {
  token: string;
  user: {
    userId: string;
    email: string;
    firstName: string;
    lastName: string;
    roleId?: string;
    roleName?: string;
    departmentId?: string;
    departmentName?: string;
    userType: string;
  };
}

export interface LogoutResponseDto {
  message: string;
}
