// Dropbox DTOs
export interface TslsToUniDropboxDto {
  tslsToUniDropboxId: string;
  tslsToUniDropboxName: string;
  openingDate: Date;
  closingDate: Date;
  isOpen: boolean;
  universityId: string;
  createdAt: Date;
  updatedAt?: Date;
}

export interface TslsToUniDropboxCreateDto {
  tslsToUniDropboxName: string;
  openingDate: Date;
  closingDate: Date;
  universityId: string;
}

export interface TslsToUniDropboxUpdateDto {
  tslsToUniDropboxName?: string;
  openingDate?: Date;
  closingDate?: Date;
  isOpen?: boolean;
}

export interface UniToTslsDropboxDto {
  uniToTslsDropboxId: string;
  uniToTslsDropboxName: string;
  openingDate: Date;
  closingDate: Date;
  isOpen: boolean;
  createdAt: Date;
  updatedAt?: Date;
}

export interface UniToTslsDropboxCreateDto {
  uniToTslsDropboxName: string;
  openingDate: Date;
  closingDate: Date;
}

export interface UniToTslsDropboxUpdateDto {
  uniToTslsDropboxName?: string;
  openingDate?: Date;
  closingDate?: Date;
  isOpen?: boolean;
}

export interface UniToTslsReportSubmissionDto {
  uniToTslsReportSubmissionId: string;
  uniToTslsReportSubmissionFilePath: string;
  submittedAt: Date;
  uniToTslsDropboxId: string;
}

export interface UniToTslsReportSubmissionCreateDto {
  uniToTslsDropboxId: string;
  file: File | Buffer;
}
