import { AuthService } from '../../../application/services/AuthService.js';
import { verifyJWT } from '../middleware/auth.js';
// Simple password hasher implementation (in production, use bcrypt)
class SimplePasswordHasher {
    hash(password) {
        // In production, use proper hashing like bcrypt
        return Buffer.from(password).toString('base64');
    }
    verify(password, hash) {
        return this.hash(password) === hash;
    }
}
// Simple audit service implementation
class SimpleAuditService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async logAsync(userId, actionType, tableName, recordId, oldValues, newValues, additionalInfo) {
        await this.prisma.auditLog.create({
            data: {
                LogId: crypto.randomUUID(),
                ActionType: actionType,
                TableName: tableName,
                RecordId: recordId,
                OldValues: oldValues,
                NewValues: newValues,
                IPAddress: '127.0.0.1', // In production, get from request
                SessionId: crypto.randomUUID(),
                UserAgent: 'API',
                AdditionalInfo: additionalInfo,
                UserId: userId
            }
        });
    }
}
export default async function authRoutes(fastify) {
    const passwordHasher = new SimplePasswordHasher();
    const auditService = new SimpleAuditService(fastify.prisma);
    const authService = new AuthService(fastify.prisma, passwordHasher, auditService);
    // Login endpoint
    fastify.post('/login', async (request, reply) => {
        try {
            const { email, password } = request.body;
            if (!email || !password) {
                return reply.status(400).send({ error: 'Invalid input.' });
            }
            const result = await authService.login({ email, password });
            return reply.status(200).send(result);
        }
        catch (error) {
            const message = error instanceof Error ? error.message : 'An error occurred';
            if (message.includes('Invalid credentials') || message.includes('locked') || message.includes('not active')) {
                return reply.status(401).send({ error: message });
            }
            return reply.status(500).send({ error: 'Internal server error' });
        }
    });
    // Logout endpoint
    fastify.post('/logout', { preHandler: verifyJWT }, async (request, reply) => {
        try {
            const userId = request.user.userId;
            await authService.logout(userId);
            return reply.status(200).send({ message: 'Logged out' });
        }
        catch (error) {
            const message = error instanceof Error ? error.message : 'An error occurred';
            if (message.includes('not found')) {
                return reply.status(404).send({ error: message });
            }
            return reply.status(500).send({ error: 'Internal server error' });
        }
    });
}
