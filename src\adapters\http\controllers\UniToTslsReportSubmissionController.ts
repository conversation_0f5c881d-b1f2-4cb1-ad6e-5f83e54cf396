// UniToTslsReportSubmission Controller
import { FastifyRequest, FastifyReply } from 'fastify';
import { UniToTslsReportSubmissionService } from '../../../application/services/UniToTslsReportSubmissionService.js';
import { UniToTslsReportSubmissionCreateDto } from '../../../application/dto/dropbox.dto.js';

export class UniToTslsReportSubmissionController {
  private submissionService: UniToTslsReportSubmissionService;

  constructor(prisma: any) {
    this.submissionService = new UniToTslsReportSubmissionService(prisma);
  }

  async submitUniToTslsReport(request: FastifyRequest<{ 
    Body: { uniToTslsDropboxId: string }; 
    Querystring: { userId: string } 
  }>, reply: FastifyReply) {
    try {
      // In a real implementation, you'd handle multipart form data properly
      // This is a simplified version
      const { uniToTslsDropboxId } = request.body;
      const { userId } = request.query;
      
      if (!uniToTslsDropboxId) {
        return reply.status(400).send({ error: 'Dropbox ID is required' });
      }
      
      // For this example, we'll create a dummy file buffer
      // In production, you'd extract the file from the multipart form data
      const dummyFile = Buffer.from('dummy file content');
      
      const createDto: UniToTslsReportSubmissionCreateDto = {
        uniToTslsDropboxId,
        file: dummyFile
      };
      
      const result = await this.submissionService.createAsync(userId, createDto);
      return reply.status(201).send(result);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';
      return reply.status(500).send({ error: message });
    }
  }
}
