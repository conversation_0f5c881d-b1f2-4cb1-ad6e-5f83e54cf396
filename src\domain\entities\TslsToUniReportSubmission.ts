// Domain Entity for TslsToUniReportSubmission
export class TslsToUniReportSubmission {
  constructor(
    public readonly tslsToUniReportSubmissionId: string,
    public readonly tslsToUniReportSubmissionName: string,
    public readonly tslsToUniReportSubmissionFilePath: string,
    public readonly submittedBy: string,
    public readonly tslsToUniDropboxId: string,
    public readonly submittedAt: Date = new Date(),
    public readonly notes: string = ''
  ) {}

  // Domain methods
  public updateNotes(newNotes: string): TslsToUniReportSubmission {
    return new TslsToUniReportSubmission(
      this.tslsToUniReportSubmissionId,
      this.tslsToUniReportSubmissionName,
      this.tslsToUniReportSubmissionFilePath,
      this.submittedBy,
      this.tslsToUniDropboxId,
      this.submittedAt,
      newNotes
    );
  }

  // Business rules
  public isValidFilePath(): boolean {
    return this.tslsToUniReportSubmissionFilePath.trim().length > 0 &&
           (this.tslsToUniReportSubmissionFilePath.endsWith('.pdf') ||
            this.tslsToUniReportSubmissionFilePath.endsWith('.doc') ||
            this.tslsToUniReportSubmissionFilePath.endsWith('.docx') ||
            this.tslsToUniReportSubmissionFilePath.endsWith('.xlsx') ||
            this.tslsToUniReportSubmissionFilePath.endsWith('.xls'));
  }

  public isRecentSubmission(hoursThreshold: number = 24): boolean {
    const now = new Date();
    const diffInHours = (now.getTime() - this.submittedAt.getTime()) / (1000 * 60 * 60);
    return diffInHours <= hoursThreshold;
  }

  public getFileExtension(): string {
    const parts = this.tslsToUniReportSubmissionFilePath.split('.');
    return parts.length > 1 ? parts[parts.length - 1].toLowerCase() : '';
  }

  public getFileName(): string {
    const parts = this.tslsToUniReportSubmissionFilePath.split('/');
    return parts[parts.length - 1];
  }

  public hasNotes(): boolean {
    return this.notes.trim().length > 0;
  }
}
