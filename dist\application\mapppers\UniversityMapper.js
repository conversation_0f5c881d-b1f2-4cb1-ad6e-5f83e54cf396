import { University } from '../../domain/entities/index.js';
export class UniversityMapper {
    // Convert from Prisma model to Domain entity
    static toDomain(prismaUniversity) {
        return new University(prismaUniversity.UniversityId, prismaUniversity.UniversityName, prismaUniversity.PrimaryEmail, prismaUniversity.ContactPerson, prismaUniversity.Phone, prismaUniversity.IsActive, prismaUniversity.CreatedAt, prismaUniversity.UpdatedAt, prismaUniversity.CCEmails || undefined, prismaUniversity.BCCEmails || undefined);
    }
    // Convert from Domain entity to Prisma model data
    static toPrisma(domainUniversity) {
        return {
            UniversityId: domainUniversity.universityId,
            UniversityName: domainUniversity.universityName,
            PrimaryEmail: domainUniversity.primaryEmail,
            CCEmails: domainUniversity.ccEmails || null,
            BCCEmails: domainUniversity.bccEmails || null,
            ContactPerson: domainUniversity.contactPerson,
            Phone: domainUniversity.phone,
            IsActive: domainUniversity.isActive,
            CreatedAt: domainUniversity.createdAt,
            UpdatedAt: domainUniversity.updatedAt
        };
    }
    // Convert array of Prisma models to Domain entities
    static toDomainArray(prismaUniversities) {
        return prismaUniversities.map(university => this.toDomain(university));
    }
}
