# Database Tables Creation Guide

## ✅ **Where to Create Your Database Tables**

Your database tables are now created in the **entities folder** as requested:

### 📁 **Entity Location**: `src/domain/entities/`

I've created all your C# models as TypeScript domain entities:

1. **PTEIUser** → `src/domain/entities/User.ts`
2. **Role** → `src/domain/entities/Role.ts`
3. **Department** → `src/domain/entities/Department.ts`
4. **Permission** → `src/domain/entities/Permission.ts`
5. **RolePermission** → `src/domain/entities/RolePermission.ts`
6. **AuditLog** → `src/domain/entities/AuditLog.ts`
7. **University** → `src/domain/entities/University.ts`
8. **UniversityUser** → `src/domain/entities/UniversityUser.ts`
9. **TslsToUniDropbox** → `src/domain/entities/TslsToUniDropbox.ts`
10. **TslsToUniReportSubmission** → `src/domain/entities/TslsToUniReportSubmission.ts`
11. **UniToTslsDropbox** → `src/domain/entities/UniToTslsDropbox.ts`
12. **UniToTslsReportSubmission** → `src/domain/entities/UniToTslsReportSubmission.ts`
13. **Enums** → `src/domain/entities/enums.ts`

### 🗄️ **Database Schema**: `prisma/schema.prisma`

The Prisma schema has been updated to match all your entities with proper:
- SQL Server data types
- Foreign key relationships
- Indexes and constraints
- Default values

## 🚀 **Next Steps to Complete Setup**

### 1. **Generate Prisma Client**
```bash
npx prisma generate
```

### 2. **Create and Run Migration**
```bash
npx prisma migrate dev --name create_all_tables
```

### 3. **Verify Database**
```bash
npx prisma studio
```

## 📋 **How to Use Your Entities**

### **Example: Creating a User**
```typescript
import { PTEIUser, UserType } from '../domain/entities/index.js';

// Create domain entity with business logic
const user = new PTEIUser(
  crypto.randomUUID(),
  '<EMAIL>',
  'hashedPassword123',
  'John',
  'Doe',
  '',
  UserType.UNIVERSITY_USER,
  'ACTIVE'
);

// Use domain methods
const lockedUser = user.lockAccount();
const isValid = user.isValidEmail();
```

### **Example: Using with Prisma**
```typescript
import { UserMapper } from '../application/mapppers/UserMapper.js';

// Convert domain entity to Prisma format
const prismaData = UserMapper.toPrisma(domainUser);

// Save to database
const savedUser = await prisma.pTEIUser.create({
  data: prismaData
});

// Convert back to domain entity
const domainUser = UserMapper.toDomain(savedUser);
```

## 🏗️ **Architecture Benefits**

✅ **Rich Domain Models** - Business logic in entities
✅ **Type Safety** - Full TypeScript support
✅ **Clean Separation** - Domain vs. Data Access
✅ **Easy Testing** - Pure domain logic
✅ **Maintainable** - Clear structure

## 📁 **File Structure**
```
src/
├── domain/
│   └── entities/           ← Your database tables as domain entities
│       ├── User.ts
│       ├── Role.ts
│       ├── Department.ts
│       ├── Permission.ts
│       ├── RolePermission.ts
│       ├── AuditLog.ts
│       ├── University.ts
│       ├── UniversityUser.ts
│       ├── TslsToUniDropbox.ts
│       ├── TslsToUniReportSubmission.ts
│       ├── UniToTslsDropbox.ts
│       ├── UniToTslsReportSubmission.ts
│       ├── enums.ts
│       └── index.ts
├── application/
│   ├── mapppers/           ← Convert between domain and Prisma
│   │   ├── UserMapper.ts
│   │   └── UniversityMapper.ts
│   └── services/           ← Business logic services
│       └── UserService.example.ts
└── infrastructure/
    └── db/                 ← Database connection
        └── prismaPlugin.ts

prisma/
└── schema.prisma          ← Database schema matching your entities
```

## 🎯 **Key Features Added**

- **Domain-Driven Design** patterns
- **Business logic** in entities (validation, state changes)
- **Type-safe** database operations
- **Clean Architecture** separation
- **Immutable entities** (functional approach)
- **Rich domain methods** for business rules
- **Proper enum handling**
- **Comprehensive relationships**

Your database tables are now properly organized in the entities folder with full business logic and type safety! 🎉
