// UniToTslsReportSubmission routes
import { FastifyInstance } from 'fastify';
import { UniToTslsReportSubmissionService } from '../../../application/services/UniToTslsReportSubmissionService.js';
import { UniToTslsReportSubmissionCreateDto } from '../../../application/dto/dropbox.dto.js';

export default async function uniToTslsReportSubmissionRoutes(fastify: FastifyInstance) {
  const submissionService = new UniToTslsReportSubmissionService(fastify.prisma);

  // Submit Uni to TSLS report
  fastify.post<{ 
    Body: { uniToTslsDropboxId: string }; 
    Querystring: { userId: string } 
  }>('/submit-uni-to-tsls-report', async (request, reply) => {
    try {
      // In a real implementation, you'd handle multipart form data properly
      // This is a simplified version
      const { uniToTslsDropboxId } = request.body;
      const { userId } = request.query;
      
      if (!uniToTslsDropboxId) {
        return reply.status(400).send({ error: 'Dropbox ID is required' });
      }
      
      // For this example, we'll create a dummy file buffer
      // In production, you'd extract the file from the multipart form data
      const dummyFile = Buffer.from('dummy file content');
      
      const createDto: UniToTslsReportSubmissionCreateDto = {
        uniToTslsDropboxId,
        file: dummyFile
      };
      
      const result = await submissionService.createAsync(userId, createDto);
      return reply.status(201).send(result);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';
      return reply.status(500).send({ error: message });
    }
  });
}
