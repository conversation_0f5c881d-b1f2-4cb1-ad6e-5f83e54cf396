// Domain Entity for Permission
export class Permission {
  constructor(
    public readonly permissionId: string,
    public readonly name: string,
    public readonly description?: string
  ) {}

  // Domain methods
  public updateDescription(newDescription: string): Permission {
    return new Permission(
      this.permissionId,
      this.name,
      newDescription
    );
  }

  // Business rules
  public isValidPermissionName(): boolean {
    return this.name.trim().length > 0 && this.name.length <= 100;
  }

  public static createSystemPermissions(): Permission[] {
    return [
      new Permission('1', 'READ_USERS', 'Can view user information'),
      new Permission('2', 'WRITE_USERS', 'Can create and update users'),
      new Permission('3', 'DELETE_USERS', 'Can delete users'),
      new Permission('4', 'READ_ROLES', 'Can view roles'),
      new Permission('5', 'WRITE_ROLES', 'Can create and update roles'),
      new Permission('6', 'DELETE_ROLES', 'Can delete roles'),
      new Permission('7', 'READ_DEPARTMENTS', 'Can view departments'),
      new Permission('8', 'WRITE_DEPARTMENTS', 'Can create and update departments'),
      new Permission('9', 'DELETE_DEPARTMENTS', 'Can delete departments'),
      new Permission('10', 'READ_AUDIT_LOGS', 'Can view audit logs'),
      new Permission('11', 'MANAGE_UNIVERSITIES', 'Can manage university settings'),
      new Permission('12', 'MANAGE_DROPBOXES', 'Can manage dropbox configurations'),
      new Permission('13', 'SUBMIT_REPORTS', 'Can submit reports'),
      new Permission('14', 'VIEW_REPORTS', 'Can view submitted reports')
    ];
  }
}
