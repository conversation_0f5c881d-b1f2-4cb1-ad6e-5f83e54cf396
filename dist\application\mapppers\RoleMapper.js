import { Role } from '../../domain/entities/index.js';
export class RoleMapper {
    // Convert from Prisma model to Domain entity
    static toDomain(prismaRole) {
        return new Role(prismaRole.RoleId, prismaRole.RoleName, prismaRole.Description || undefined, prismaRole.IsActive, prismaRole.CreatedAt);
    }
    // Convert from Domain entity to Prisma format
    static toPrisma(role) {
        return {
            RoleId: role.roleId,
            RoleName: role.roleName,
            Description: role.description || null,
            IsActive: role.isActive,
            CreatedAt: role.createdAt
        };
    }
}
