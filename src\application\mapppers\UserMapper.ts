// Mapper to convert between Prisma models and Domain entities
import { PTEIUser as PrismaUser } from '../../generated/prisma/index.js';
import { PTEIUser, UserType } from '../../domain/entities/index.js';

export class UserMapper {
  // Convert from Prisma model to Domain entity
  static toDomain(prismaUser: PrismaUser): PTEIUser {
    return new PTEIUser(
      prismaUser.UserId,
      prismaUser.email,
      prismaUser.HashedPassword,
      prismaUser.FirstName,
      prismaUser.LastName,
      prismaUser.UserProfilePicPath,
      prismaUser.UserType as UserType,
      prismaUser.AccountStatus,
      prismaUser.IsAccountLockedOut,
      prismaUser.FailedLoginAttempts,
      prismaUser.IsUserLoggedIn,
      prismaUser.UserCreatedAt,
      prismaUser.UserModifiedAt || undefined,
      prismaUser.EmailVerificationToken || undefined,
      prismaUser.EmailVerificationTokenExpiry || undefined,
      prismaUser.PasswordResetToken || undefined,
      prismaUser.PasswordResetTokenExpiry || undefined,
      prismaUser.RoleId || undefined,
      prismaUser.DepartmentId || undefined
    );
  }

  // Convert from Domain entity to Prisma model data
  static toPrisma(domainUser: PTEIUser): Omit<PrismaUser, 'Role' | 'Department' | 'AuditLogs' | 'UniversityUsers'> {
    return {
      UserId: domainUser.userId,
      email: domainUser.email,
      HashedPassword: domainUser.hashedPassword,
      FirstName: domainUser.firstName,
      LastName: domainUser.lastName,
      UserProfilePicPath: domainUser.userProfilePicPath,
      UserType: domainUser.userType,
      AccountStatus: domainUser.accountStatus,
      IsAccountLockedOut: domainUser.isAccountLockedOut,
      FailedLoginAttempts: domainUser.failedLoginAttempts,
      IsUserLoggedIn: domainUser.isUserLoggedIn,
      UserCreatedAt: domainUser.userCreatedAt,
      UserModifiedAt: domainUser.userModifiedAt || null,
      EmailVerificationToken: domainUser.emailVerificationToken || null,
      EmailVerificationTokenExpiry: domainUser.emailVerificationTokenExpiry || null,
      PasswordResetToken: domainUser.passwordResetToken || null,
      PasswordResetTokenExpiry: domainUser.passwordResetTokenExpiry || null,
      RoleId: domainUser.roleId || null,
      DepartmentId: domainUser.departmentId || null
    };
  }

  // Convert array of Prisma models to Domain entities
  static toDomainArray(prismaUsers: PrismaUser[]): PTEIUser[] {
    return prismaUsers.map(user => this.toDomain(user));
  }
}
