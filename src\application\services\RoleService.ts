// Role Service
import { PrismaClient } from '../../generated/prisma/index.js';
import { Role } from '../../domain/entities/index.js';
import { RoleMapper } from '../mapppers/RoleMapper.js';
import { RoleDto, CreateRoleDto, UpdateRoleDto } from '../dto/role.dto.js';

export class RoleService {
  constructor(private prisma: PrismaClient) {}

  async getAllRoles(): Promise<RoleDto[]> {
    const prismaRoles = await this.prisma.role.findMany({
      where: { IsActive: true },
      orderBy: { RoleName: 'asc' }
    });

    return prismaRoles.map(role => ({
      roleId: role.RoleId,
      roleName: role.RoleName,
      description: role.Description || undefined,
      isActive: role.IsActive,
      createdAt: role.CreatedAt
    }));
  }

  async getRoleById(id: string): Promise<RoleDto | null> {
    const prismaRole = await this.prisma.role.findUnique({
      where: { RoleId: id }
    });

    if (!prismaRole) {
      return null;
    }

    return {
      roleId: prismaRole.RoleId,
      roleName: prismaRole.RoleName,
      description: prismaRole.Description || undefined,
      isActive: prismaRole.IsActive,
      createdAt: prismaRole.CreatedAt
    };
  }

  async createRole(createDto: CreateRoleDto): Promise<RoleDto> {
    const role = new Role(
      crypto.randomUUID(),
      createDto.roleName,
      createDto.description,
      true,
      new Date()
    );

    const prismaData = RoleMapper.toPrisma(role);
    const createdRole = await this.prisma.role.create({
      data: prismaData
    });

    return {
      roleId: createdRole.RoleId,
      roleName: createdRole.RoleName,
      description: createdRole.Description || undefined,
      isActive: createdRole.IsActive,
      createdAt: createdRole.CreatedAt
    };
  }

  async updateRole(id: string, updateDto: UpdateRoleDto): Promise<RoleDto | null> {
    const existingRole = await this.prisma.role.findUnique({
      where: { RoleId: id }
    });

    if (!existingRole) {
      return null;
    }

    const updatedRole = await this.prisma.role.update({
      where: { RoleId: id },
      data: {
        RoleName: updateDto.roleName ?? existingRole.RoleName,
        Description: updateDto.description ?? existingRole.Description,
        IsActive: updateDto.isActive ?? existingRole.IsActive
      }
    });

    return {
      roleId: updatedRole.RoleId,
      roleName: updatedRole.RoleName,
      description: updatedRole.Description || undefined,
      isActive: updatedRole.IsActive,
      createdAt: updatedRole.CreatedAt
    };
  }

  async deleteRole(id: string): Promise<void> {
    // Soft delete by setting IsActive to false
    await this.prisma.role.update({
      where: { RoleId: id },
      data: { IsActive: false }
    });
  }
}
