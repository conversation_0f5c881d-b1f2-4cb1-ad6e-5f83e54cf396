// TslsToUniDropbox Controller
import { FastifyRequest, FastifyReply } from 'fastify';
import { TslsToUniDropboxService } from '../../../application/services/TslsToUniDropboxService.js';
import { TslsToUniDropboxCreateDto, TslsToUniDropboxUpdateDto } from '../../../application/dto/dropbox.dto.js';

export class TslsToUniDropboxController {
  private dropboxService: TslsToUniDropboxService;

  constructor(prisma: any) {
    this.dropboxService = new TslsToUniDropboxService(prisma);
  }

  async createTslsToUniDropbox(request: FastifyRequest<{ Body: TslsToUniDropboxCreateDto; Querystring: { userId: string } }>, reply: FastifyReply) {
    try {
      const createDto = request.body;
      const { userId } = request.query;
      
      if (!createDto.tslsToUniDropboxName || !createDto.universityId || !createDto.openingDate || !createDto.closingDate) {
        return reply.status(400).send({ error: 'Missing required fields' });
      }
      
      const result = await this.dropboxService.createAsync(userId, createDto);
      return reply.status(201).send(result);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';
      
      if (message.includes('already exists')) {
        return reply.status(409).send({ error: message });
      }
      
      return reply.status(500).send({ error: message });
    }
  }

  async updateTslsToUniDropbox(request: FastifyRequest<{ 
    Params: { dropboxId: string }; 
    Body: TslsToUniDropboxUpdateDto; 
    Querystring: { userId: string } 
  }>, reply: FastifyReply) {
    try {
      const { dropboxId } = request.params;
      const updateDto = request.body;
      const { userId } = request.query;
      
      const result = await this.dropboxService.updateAsync(userId, dropboxId, updateDto);
      return reply.status(200).send(result);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';
      return reply.status(500).send({ error: message });
    }
  }

  async getDropboxById(request: FastifyRequest<{ Params: { dropboxId: string } }>, reply: FastifyReply) {
    try {
      const { dropboxId } = request.params;
      const result = await this.dropboxService.getByIdAsync(dropboxId);
      return reply.status(200).send(result);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';
      return reply.status(500).send({ error: message });
    }
  }

  async getDropboxByUniversity(request: FastifyRequest<{ Params: { universityId: string } }>, reply: FastifyReply) {
    try {
      const { universityId } = request.params;
      const result = await this.dropboxService.getByUniversityAsync(universityId);
      return reply.status(200).send(result);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';
      return reply.status(500).send({ error: message });
    }
  }

  async getUniversitiesWithDropboxes(request: FastifyRequest, reply: FastifyReply) {
    try {
      const result = await this.dropboxService.getUniversitiesWithDropboxesAsync();
      return reply.status(200).send(result);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';
      return reply.status(500).send({ error: message });
    }
  }
}
