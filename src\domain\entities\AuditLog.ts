// Domain Entity for AuditLog
export class AuditLog {
  constructor(
    public readonly logId: string,
    public readonly actionType: string,
    public readonly tableName: string,
    public readonly recordId: string,
    public readonly ipAddress: string,
    public readonly sessionId: string,
    public readonly userAgent: string,
    public readonly userId: string,
    public readonly oldValues?: string,
    public readonly newValues?: string,
    public readonly actionTimestamp: Date = new Date(),
    public readonly additionalInfo?: string
  ) {}

  // Domain methods
  public static createForCreate(
    logId: string,
    tableName: string,
    recordId: string,
    newValues: string,
    userId: string,
    ipAddress: string,
    sessionId: string,
    userAgent: string,
    additionalInfo?: string
  ): AuditLog {
    return new AuditLog(
      logId,
      'CREATE',
      tableName,
      recordId,
      ipAddress,
      sessionId,
      userAgent,
      userId,
      undefined,
      newValues,
      new Date(),
      additionalInfo
    );
  }

  public static createForUpdate(
    logId: string,
    tableName: string,
    recordId: string,
    oldValues: string,
    newValues: string,
    userId: string,
    ipAddress: string,
    sessionId: string,
    userAgent: string,
    additionalInfo?: string
  ): AuditLog {
    return new AuditLog(
      logId,
      'UPDATE',
      tableName,
      recordId,
      ipAddress,
      sessionId,
      userAgent,
      userId,
      oldValues,
      newValues,
      new Date(),
      additionalInfo
    );
  }

  public static createForDelete(
    logId: string,
    tableName: string,
    recordId: string,
    oldValues: string,
    userId: string,
    ipAddress: string,
    sessionId: string,
    userAgent: string,
    additionalInfo?: string
  ): AuditLog {
    return new AuditLog(
      logId,
      'DELETE',
      tableName,
      recordId,
      ipAddress,
      sessionId,
      userAgent,
      userId,
      oldValues,
      undefined,
      new Date(),
      additionalInfo
    );
  }

  // Business rules
  public isRecentAction(minutesThreshold: number = 30): boolean {
    const now = new Date();
    const diffInMinutes = (now.getTime() - this.actionTimestamp.getTime()) / (1000 * 60);
    return diffInMinutes <= minutesThreshold;
  }

  public isCriticalAction(): boolean {
    const criticalActions = ['DELETE', 'UPDATE_PASSWORD', 'LOCK_ACCOUNT', 'UNLOCK_ACCOUNT'];
    return criticalActions.includes(this.actionType);
  }
}
