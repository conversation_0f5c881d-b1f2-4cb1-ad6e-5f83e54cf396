// Domain Entity for RolePermission (Junction table)
export class RolePermission {
  constructor(
    public readonly roleId: string,
    public readonly permissionId: string
  ) {}

  // Domain methods
  public static createRolePermissions(roleId: string, permissionIds: string[]): RolePermission[] {
    return permissionIds.map(permissionId => new RolePermission(roleId, permissionId));
  }

  // Business rules
  public equals(other: RolePermission): boolean {
    return this.roleId === other.roleId && this.permissionId === other.permissionId;
  }

  public getCompositeKey(): string {
    return `${this.roleId}-${this.permissionId}`;
  }
}
