// Authentication Service
import { PrismaClient } from '../../generated/prisma/index.js';
import { PTEIUser, UserType } from '../../domain/entities/index.js';
import { UserMapper } from '../mapppers/UserMapper.js';
import { LoginDto, LoginResponseDto } from '../dto/auth.dto.js';
import jwt from 'jsonwebtoken';
import { env } from '../../infrastructure/config/env.js';

export interface IPasswordHasher {
  hash(password: string): string;
  verify(password: string, hash: string): boolean;
}

export interface IAuditService {
  logAsync(userId: string, actionType: string, tableName: string, recordId: string, oldValues?: string, newValues?: string, additionalInfo?: string): Promise<void>;
}

export class AuthService {
  constructor(
    private prisma: PrismaClient,
    private passwordHasher: IPasswordHasher,
    private auditService: IAuditService
  ) {}

  async login(loginDto: LoginDto): Promise<LoginResponseDto> {
    // Get user by email with relations
    const prismaUser = await this.prisma.pTEIUser.findUnique({
      where: { email: loginDto.email },
      include: {
        Role: true,
        Department: true
      }
    });

    if (!prismaUser) {
      throw new Error('Invalid credentials');
    }

    const user = UserMapper.toDomain(prismaUser);

    if (user.isAccountLockedOut) {
      throw new Error('Account is locked due to too many failed login attempts.');
    }

    if (user.accountStatus?.toLowerCase() !== 'active') {
      throw new Error('Account is not active.');
    }

    const hashedInput = this.passwordHasher.hash(loginDto.password);

    if (user.hashedPassword !== hashedInput) {
      // Update failed login attempts
      const updatedUser = user.incrementFailedLoginAttempts();
      const updatedPrismaData = UserMapper.toPrisma(updatedUser);
      
      await this.prisma.pTEIUser.update({
        where: { UserId: user.userId },
        data: updatedPrismaData
      });

      await this.auditService.logAsync(user.userId, 'LOGIN_FAILED', 'PTEIUser', user.userId, undefined, undefined, 'Invalid login');
      throw new Error('Invalid credentials.');
    }

    // Reset failed login attempts and set logged in
    const loggedInUser = user.resetFailedLoginAttempts().setLoggedIn(true);
    const loggedInPrismaData = UserMapper.toPrisma(loggedInUser);
    
    await this.prisma.pTEIUser.update({
      where: { UserId: user.userId },
      data: loggedInPrismaData
    });

    await this.auditService.logAsync(user.userId, 'LOGIN_SUCCESS', 'PTEIUser', user.userId, undefined, undefined, 'User logged in');

    const token = this.generateJwtToken(user, prismaUser.Role?.RoleName);

    return {
      token,
      user: {
        userId: user.userId,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        roleId: user.roleId,
        roleName: prismaUser.Role?.RoleName,
        departmentId: user.departmentId,
        departmentName: prismaUser.Department?.DepartmentName,
        userType: user.userType.toString()
      }
    };
  }

  async logout(userId: string): Promise<void> {
    const prismaUser = await this.prisma.pTEIUser.findUnique({
      where: { UserId: userId }
    });

    if (!prismaUser) {
      throw new Error('User not found');
    }

    const user = UserMapper.toDomain(prismaUser);
    const loggedOutUser = user.setLoggedIn(false);
    const loggedOutPrismaData = UserMapper.toPrisma(loggedOutUser);

    await this.prisma.pTEIUser.update({
      where: { UserId: userId },
      data: loggedOutPrismaData
    });

    await this.auditService.logAsync(userId, 'LOGOUT', 'PTEIUser', userId, undefined, undefined, 'User logged out');
  }

  private generateJwtToken(user: PTEIUser, roleName?: string): string {
    if (!roleName) {
      throw new Error(`Role is not set for user ${user.email}.`);
    }

    const payload = {
      sub: user.userId,
      email: user.email,
      role: roleName,
      userType: user.userType.toString()
    };

    return jwt.sign(payload, env.JWT_SECRET, { expiresIn: '8h' });
  }
}
