// TslsToUniDropbox routes
import { FastifyInstance } from 'fastify';
import { TslsToUniDropboxService } from '../../../application/services/TslsToUniDropboxService.js';
import { TslsToUniDropboxCreateDto, TslsToUniDropboxUpdateDto } from '../../../application/dto/dropbox.dto.js';

export default async function tslsToUniDropboxRoutes(fastify: FastifyInstance) {
  const dropboxService = new TslsToUniDropboxService(fastify.prisma);

  // Create TSLS to Uni dropbox
  fastify.post<{ Body: TslsToUniDropboxCreateDto; Querystring: { userId: string } }>('/create-tsls-to-uni-dropbox', async (request, reply) => {
    try {
      const createDto = request.body;
      const { userId } = request.query;
      
      if (!createDto.tslsToUniDropboxName || !createDto.universityId || !createDto.openingDate || !createDto.closingDate) {
        return reply.status(400).send({ error: 'Missing required fields' });
      }
      
      const result = await dropboxService.createAsync(userId, createDto);
      return reply.status(201).send(result);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';
      
      if (message.includes('already exists')) {
        return reply.status(409).send({ error: message });
      }
      
      return reply.status(500).send({ error: message });
    }
  });

  // Update TSLS to Uni dropbox
  fastify.put<{ 
    Params: { dropboxId: string }; 
    Body: TslsToUniDropboxUpdateDto; 
    Querystring: { userId: string } 
  }>('/update-tsls-to-uni-dropbox/:dropboxId', async (request, reply) => {
    try {
      const { dropboxId } = request.params;
      const updateDto = request.body;
      const { userId } = request.query;
      
      const result = await dropboxService.updateAsync(userId, dropboxId, updateDto);
      return reply.status(200).send(result);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';
      return reply.status(500).send({ error: message });
    }
  });

  // Get dropbox by ID
  fastify.get<{ Params: { dropboxId: string } }>('/get-dropbox-by-id/:dropboxId', async (request, reply) => {
    try {
      const { dropboxId } = request.params;
      const result = await dropboxService.getByIdAsync(dropboxId);
      return reply.status(200).send(result);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';
      return reply.status(500).send({ error: message });
    }
  });

  // Get dropbox by university
  fastify.get<{ Params: { universityId: string } }>('/get-dropbox-by-university/:universityId', async (request, reply) => {
    try {
      const { universityId } = request.params;
      const result = await dropboxService.getByUniversityAsync(universityId);
      return reply.status(200).send(result);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';
      return reply.status(500).send({ error: message });
    }
  });

  // Get universities with dropboxes
  fastify.get('/get-universities-with-dropboxes', async (request, reply) => {
    try {
      const result = await dropboxService.getUniversitiesWithDropboxesAsync();
      return reply.status(200).send(result);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';
      return reply.status(500).send({ error: message });
    }
  });
}
