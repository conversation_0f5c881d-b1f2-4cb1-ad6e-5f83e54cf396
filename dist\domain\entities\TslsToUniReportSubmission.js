// Domain Entity for TslsToUniReportSubmission
export class TslsToUniReportSubmission {
    tslsToUniReportSubmissionId;
    tslsToUniReportSubmissionName;
    tslsToUniReportSubmissionFilePath;
    submittedBy;
    tslsToUniDropboxId;
    submittedAt;
    notes;
    constructor(tslsToUniReportSubmissionId, tslsToUniReportSubmissionName, tslsToUniReportSubmissionFilePath, submittedBy, tslsToUniDropboxId, submittedAt = new Date(), notes = '') {
        this.tslsToUniReportSubmissionId = tslsToUniReportSubmissionId;
        this.tslsToUniReportSubmissionName = tslsToUniReportSubmissionName;
        this.tslsToUniReportSubmissionFilePath = tslsToUniReportSubmissionFilePath;
        this.submittedBy = submittedBy;
        this.tslsToUniDropboxId = tslsToUniDropboxId;
        this.submittedAt = submittedAt;
        this.notes = notes;
    }
    // Domain methods
    updateNotes(newNotes) {
        return new TslsToUniReportSubmission(this.tslsToUniReportSubmissionId, this.tslsToUniReportSubmissionName, this.tslsToUniReportSubmissionFilePath, this.submittedBy, this.tslsToUniDropboxId, this.submittedAt, newNotes);
    }
    // Business rules
    isValidFilePath() {
        return this.tslsToUniReportSubmissionFilePath.trim().length > 0 &&
            (this.tslsToUniReportSubmissionFilePath.endsWith('.pdf') ||
                this.tslsToUniReportSubmissionFilePath.endsWith('.doc') ||
                this.tslsToUniReportSubmissionFilePath.endsWith('.docx') ||
                this.tslsToUniReportSubmissionFilePath.endsWith('.xlsx') ||
                this.tslsToUniReportSubmissionFilePath.endsWith('.xls'));
    }
    isRecentSubmission(hoursThreshold = 24) {
        const now = new Date();
        const diffInHours = (now.getTime() - this.submittedAt.getTime()) / (1000 * 60 * 60);
        return diffInHours <= hoursThreshold;
    }
    getFileExtension() {
        const parts = this.tslsToUniReportSubmissionFilePath.split('.');
        return parts.length > 1 ? parts[parts.length - 1].toLowerCase() : '';
    }
    getFileName() {
        const parts = this.tslsToUniReportSubmissionFilePath.split('/');
        return parts[parts.length - 1];
    }
    hasNotes() {
        return this.notes.trim().length > 0;
    }
}
