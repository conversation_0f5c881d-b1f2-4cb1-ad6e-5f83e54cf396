// Domain Entity for UniversityUser (Junction table)
export class UniversityUser {
  constructor(
    public readonly universityUserId: string,
    public readonly userId: string,
    public readonly universityId: string,
    public readonly createdAt: Date = new Date(),
    public readonly isActive: boolean = true
  ) {}

  // Domain methods
  public deactivate(): UniversityUser {
    return new UniversityUser(
      this.universityUserId,
      this.userId,
      this.universityId,
      this.createdAt,
      false
    );
  }

  public activate(): UniversityUser {
    return new UniversityUser(
      this.universityUserId,
      this.userId,
      this.universityId,
      this.createdAt,
      true
    );
  }

  // Business rules
  public isValidForOperations(): boolean {
    return this.isActive;
  }

  public equals(other: UniversityUser): boolean {
    return this.userId === other.userId && this.universityId === other.universityId;
  }

  public getCompositeKey(): string {
    return `${this.userId}-${this.universityId}`;
  }
}
