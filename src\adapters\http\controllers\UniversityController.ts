// University Controller
import { FastifyRequest, FastifyReply } from 'fastify';
import { UniversityService } from '../../../application/services/UniversityService.js';
import { CreateUniversityDto, UpdateUniversityDto } from '../../../application/dto/university.dto.js';

export class UniversityController {
  private universityService: UniversityService;

  constructor(prisma: any) {
    this.universityService = new UniversityService(prisma);
  }

  async createUniversity(request: FastifyRequest<{ Body: CreateUniversityDto }>, reply: FastifyReply) {
    try {
      const createDto = request.body;
      const userId = request.user!.userId;
      
      if (!createDto.universityName || !createDto.primaryEmail || !createDto.contactPerson || !createDto.phone) {
        return reply.status(400).send({ error: 'Missing required fields' });
      }
      
      const university = await this.universityService.createUniversity(createDto, userId);
      return reply.status(200).send(university);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';
      
      if (message.includes('Unauthorized')) {
        return reply.status(403).send({ error: message });
      }
      
      return reply.status(400).send({ error: message });
    }
  }

  async updateUniversity(request: FastifyRequest<{ Body: UpdateUniversityDto }>, reply: FastifyReply) {
    try {
      const updateDto = request.body;
      const userId = request.user!.userId;
      
      if (!updateDto.universityId) {
        return reply.status(400).send({ error: 'University ID is required' });
      }
      
      const university = await this.universityService.updateUniversity(updateDto, userId);
      return reply.status(200).send(university);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';
      
      if (message.includes('Unauthorized')) {
        return reply.status(403).send({ error: message });
      }
      
      return reply.status(400).send({ error: message });
    }
  }

  async getUniversityById(request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) {
    try {
      const { id } = request.params;
      const university = await this.universityService.getUniversityById(id);
      
      if (!university) {
        return reply.status(404).send({ error: 'University not found' });
      }
      
      return reply.status(200).send(university);
    } catch (error) {
      return reply.status(500).send({ error: 'Internal server error' });
    }
  }

  async getAllUniversities(request: FastifyRequest, reply: FastifyReply) {
    try {
      const universities = await this.universityService.getAllUniversities();
      return reply.status(200).send(universities);
    } catch (error) {
      return reply.status(500).send({ error: 'Internal server error' });
    }
  }

  async deleteUniversity(request: FastifyRequest<{ Querystring: { id: string } }>, reply: FastifyReply) {
    try {
      const { id } = request.query;
      const userId = request.user!.userId;
      
      if (!id) {
        return reply.status(400).send({ error: 'University ID is required' });
      }
      
      const success = await this.universityService.deleteUniversity(id, userId);
      
      if (!success) {
        return reply.status(404).send({ error: 'University not found' });
      }
      
      return reply.status(200).send({ message: 'University deactivated' });
    } catch (error) {
      return reply.status(500).send({ error: 'Internal server error' });
    }
  }
}
