// Role Controller
import { FastifyRequest, FastifyReply } from 'fastify';
import { RoleService } from '../../../application/services/RoleService.js';
import { CreateRoleDto, UpdateRoleDto } from '../../../application/dto/role.dto.js';

export class RoleController {
  private roleService: RoleService;

  constructor(prisma: any) {
    this.roleService = new RoleService(prisma);
  }

  async getAllRoles(request: FastifyRequest, reply: FastifyReply) {
    try {
      const roles = await this.roleService.getAllRoles();
      return reply.status(200).send(roles);
    } catch (error) {
      return reply.status(500).send({ error: 'Internal server error' });
    }
  }

  async getRoleById(request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) {
    try {
      const { id } = request.params;
      const role = await this.roleService.getRoleById(id);
      
      if (!role) {
        return reply.status(404).send({ error: 'Role not found' });
      }
      
      return reply.status(200).send(role);
    } catch (error) {
      return reply.status(500).send({ error: 'Internal server error' });
    }
  }

  async createRole(request: FastifyRequest<{ Body: CreateRoleDto }>, reply: FastifyReply) {
    try {
      const createDto = request.body;
      
      if (!createDto.roleName) {
        return reply.status(400).send({ error: 'Role name is required' });
      }
      
      const createdRole = await this.roleService.createRole(createDto);
      return reply.status(201).send(createdRole);
    } catch (error) {
      return reply.status(500).send({ error: 'Internal server error' });
    }
  }

  async updateRole(request: FastifyRequest<{ Params: { id: string }; Body: UpdateRoleDto }>, reply: FastifyReply) {
    try {
      const { id } = request.params;
      const updateDto = request.body;
      
      const updatedRole = await this.roleService.updateRole(id, updateDto);
      
      if (!updatedRole) {
        return reply.status(404).send({ error: 'Role not found' });
      }
      
      return reply.status(204).send();
    } catch (error) {
      return reply.status(500).send({ error: 'Internal server error' });
    }
  }

  async deleteRole(request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) {
    try {
      const { id } = request.params;
      await this.roleService.deleteRole(id);
      return reply.status(204).send();
    } catch (error) {
      return reply.status(500).send({ error: 'Internal server error' });
    }
  }
}
