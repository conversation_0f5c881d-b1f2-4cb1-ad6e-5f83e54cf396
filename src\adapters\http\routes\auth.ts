// Authentication routes
import { FastifyInstance } from 'fastify';
import { AuthController } from '../controllers/AuthController.js';
import { verifyJWT } from '../middleware/auth.js';
import { LoginDto } from '../../../application/dto/auth.dto.js';


export default async function authRoutes(fastify: FastifyInstance) {
  const authController = new AuthController(fastify.prisma);

  // Login endpoint
  fastify.post<{ Body: LoginDto }>('/login', authController.login.bind(authController));

  // Logout endpoint
  fastify.post('/logout', { preHandler: verifyJWT }, authController.logout.bind(authController));
}
