// Department Controller
import { FastifyRequest, FastifyReply } from 'fastify';
import { DepartmentService } from '../../../application/services/DepartmentService.js';
import { CreateDepartmentDto, UpdateDepartmentDto } from '../../../application/dto/department.dto.js';

export class DepartmentController {
  private departmentService: DepartmentService;

  constructor(prisma: any) {
    this.departmentService = new DepartmentService(prisma);
  }

  async getAllDepartments(request: FastifyRequest, reply: FastifyReply) {
    try {
      const departments = await this.departmentService.getAllDepartments();
      return reply.status(200).send(departments);
    } catch (error) {
      return reply.status(500).send({ error: 'Internal server error' });
    }
  }

  async getDepartmentById(request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) {
    try {
      const { id } = request.params;
      const department = await this.departmentService.getDepartmentById(id);
      
      if (!department) {
        return reply.status(404).send({ error: 'Department not found' });
      }
      
      return reply.status(200).send(department);
    } catch (error) {
      return reply.status(500).send({ error: 'Internal server error' });
    }
  }

  async createDepartment(request: FastifyRequest<{ Body: CreateDepartmentDto }>, reply: FastifyReply) {
    try {
      const createDto = request.body;
      
      if (!createDto.departmentName) {
        return reply.status(400).send({ error: 'Department name is required' });
      }
      
      const createdDepartment = await this.departmentService.createDepartment(createDto);
      return reply.status(201).send(createdDepartment);
    } catch (error) {
      return reply.status(500).send({ error: 'Internal server error' });
    }
  }

  async updateDepartment(request: FastifyRequest<{ Params: { id: string }; Body: UpdateDepartmentDto }>, reply: FastifyReply) {
    try {
      const { id } = request.params;
      const updateDto = request.body;
      
      const updatedDepartment = await this.departmentService.updateDepartment(id, updateDto);
      
      if (!updatedDepartment) {
        return reply.status(404).send({ error: 'Department not found' });
      }
      
      return reply.status(204).send();
    } catch (error) {
      return reply.status(500).send({ error: 'Internal server error' });
    }
  }

  async deleteDepartment(request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) {
    try {
      const { id } = request.params;
      await this.departmentService.deleteDepartment(id);
      return reply.status(204).send();
    } catch (error) {
      return reply.status(500).send({ error: 'Internal server error' });
    }
  }
}
