// University routes
import { FastifyInstance } from 'fastify';
import { UniversityService } from '../../../application/services/UniversityService.js';
import { CreateUniversityDto, UpdateUniversityDto } from '../../../application/dto/university.dto.js';
import { verifyJWT } from '../middleware/auth.js';

export default async function universityRoutes(fastify: FastifyInstance) {
  const universityService = new UniversityService(fastify.prisma);

  // All routes require authentication
  fastify.addHook('preHandler', verifyJWT);

  // Create new university
  fastify.post<{ Body: CreateUniversityDto }>('/create-new-university', async (request, reply) => {
    try {
      const createDto = request.body;
      const userId = request.user!.userId;
      
      if (!createDto.universityName || !createDto.primaryEmail || !createDto.contactPerson || !createDto.phone) {
        return reply.status(400).send({ error: 'Missing required fields' });
      }
      
      const university = await universityService.createUniversity(createDto, userId);
      return reply.status(200).send(university);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';
      
      if (message.includes('Unauthorized')) {
        return reply.status(403).send({ error: message });
      }
      
      return reply.status(400).send({ error: message });
    }
  });

  // Update university
  fastify.post<{ Body: UpdateUniversityDto }>('/update-university', async (request, reply) => {
    try {
      const updateDto = request.body;
      const userId = request.user!.userId;
      
      if (!updateDto.universityId) {
        return reply.status(400).send({ error: 'University ID is required' });
      }
      
      const university = await universityService.updateUniversity(updateDto, userId);
      return reply.status(200).send(university);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';
      
      if (message.includes('Unauthorized')) {
        return reply.status(403).send({ error: message });
      }
      
      return reply.status(400).send({ error: message });
    }
  });

  // Get university by ID
  fastify.get<{ Params: { id: string } }>('/get-university-by-id/:id', async (request, reply) => {
    try {
      const { id } = request.params;
      const university = await universityService.getUniversityById(id);
      
      if (!university) {
        return reply.status(404).send({ error: 'University not found' });
      }
      
      return reply.status(200).send(university);
    } catch (error) {
      return reply.status(500).send({ error: 'Internal server error' });
    }
  });

  // Get all universities
  fastify.get('/get-all-universities', async (request, reply) => {
    try {
      const universities = await universityService.getAllUniversities();
      return reply.status(200).send(universities);
    } catch (error) {
      return reply.status(500).send({ error: 'Internal server error' });
    }
  });

  // Delete university
  fastify.post<{ Querystring: { id: string } }>('/delete-university', async (request, reply) => {
    try {
      const { id } = request.query;
      const userId = request.user!.userId;
      
      if (!id) {
        return reply.status(400).send({ error: 'University ID is required' });
      }
      
      const success = await universityService.deleteUniversity(id, userId);
      
      if (!success) {
        return reply.status(404).send({ error: 'University not found' });
      }
      
      return reply.status(200).send({ message: 'University deactivated' });
    } catch (error) {
      return reply.status(500).send({ error: 'Internal server error' });
    }
  });
}
