// TslsToUniDropbox Service
import { PrismaClient } from '../../generated/prisma/index.js';
import { TslsToUniDropbox } from '../../domain/entities/index.js';
import { TslsToUniDropboxDto, TslsToUniDropboxCreateDto, TslsToUniDropboxUpdateDto } from '../dto/dropbox.dto.js';

export class TslsToUniDropboxService {
  constructor(private prisma: PrismaClient) {}

  async createAsync(userId: string, createDto: TslsToUniDropboxCreateDto): Promise<TslsToUniDropboxDto> {
    // Check if university exists
    const university = await this.prisma.university.findUnique({
      where: { UniversityId: createDto.universityId }
    });

    if (!university) {
      throw new Error('University not found');
    }

    // Check if dropbox already exists for this university
    const existingDropbox = await this.prisma.tslsToUniDropbox.findFirst({
      where: {
        UniversityId: createDto.universityId,
        IsOpenStatus: true
      }
    });

    if (existingDropbox) {
      throw new InvalidOperationException('An active dropbox already exists for this university');
    }

    // Validate date range
    if (createDto.openingDate >= createDto.closingDate) {
      throw new Error('Invalid date range: opening date must be before closing date');
    }

    const createdDropbox = await this.prisma.tslsToUniDropbox.create({
      data: {
        TslsToUniDropboxId: crypto.randomUUID(),
        TslsToUniDropboxName: createDto.tslsToUniDropboxName,
        UniversityId: createDto.universityId,
        Year: new Date().getFullYear(),
        ModeOfStudy: 'FULL_TIME', // Default value
        Term: 1, // Default value
        BatchNumber: 1, // Default value
        OpeningDate: createDto.openingDate,
        ClosingDate: createDto.closingDate,
        IsOpenStatus: true,
        CreatedAt: new Date(),
        UpdatedAt: new Date()
      }
    });

    return {
      tslsToUniDropboxId: createdDropbox.TslsToUniDropboxId,
      tslsToUniDropboxName: createdDropbox.TslsToUniDropboxName,
      openingDate: createdDropbox.OpeningDate,
      closingDate: createdDropbox.ClosingDate,
      isOpen: createdDropbox.IsOpenStatus,
      universityId: createdDropbox.UniversityId,
      createdAt: createdDropbox.CreatedAt,
      updatedAt: createdDropbox.UpdatedAt || undefined
    };
  }

  async updateAsync(userId: string, dropboxId: string, updateDto: TslsToUniDropboxUpdateDto): Promise<TslsToUniDropboxDto> {
    const existingDropbox = await this.prisma.tslsToUniDropbox.findUnique({
      where: { TslsToUniDropboxId: dropboxId }
    });

    if (!existingDropbox) {
      throw new Error('Dropbox not found');
    }

    const updatedDropbox = await this.prisma.tslsToUniDropbox.update({
      where: { TslsToUniDropboxId: dropboxId },
      data: {
        TslsToUniDropboxName: updateDto.tslsToUniDropboxName ?? existingDropbox.TslsToUniDropboxName,
        OpeningDate: updateDto.openingDate ?? existingDropbox.OpeningDate,
        ClosingDate: updateDto.closingDate ?? existingDropbox.ClosingDate,
        IsOpenStatus: updateDto.isOpen ?? existingDropbox.IsOpenStatus,
        UpdatedAt: new Date()
      }
    });

    return {
      tslsToUniDropboxId: updatedDropbox.TslsToUniDropboxId,
      tslsToUniDropboxName: updatedDropbox.TslsToUniDropboxName,
      openingDate: updatedDropbox.OpeningDate,
      closingDate: updatedDropbox.ClosingDate,
      isOpen: updatedDropbox.IsOpenStatus,
      universityId: updatedDropbox.UniversityId,
      createdAt: updatedDropbox.CreatedAt,
      updatedAt: updatedDropbox.UpdatedAt || undefined
    };
  }

  async getByIdAsync(dropboxId: string): Promise<TslsToUniDropboxDto> {
    const dropbox = await this.prisma.tslsToUniDropbox.findUnique({
      where: { TslsToUniDropboxId: dropboxId }
    });

    if (!dropbox) {
      throw new Error('Dropbox not found');
    }

    return {
      tslsToUniDropboxId: dropbox.TslsToUniDropboxId,
      tslsToUniDropboxName: dropbox.TslsToUniDropboxName,
      openingDate: dropbox.OpeningDate,
      closingDate: dropbox.ClosingDate,
      isOpen: dropbox.IsOpenStatus,
      universityId: dropbox.UniversityId,
      createdAt: dropbox.CreatedAt,
      updatedAt: dropbox.UpdatedAt || undefined
    };
  }

  async getByUniversityAsync(universityId: string): Promise<TslsToUniDropboxDto[]> {
    const dropboxes = await this.prisma.tslsToUniDropbox.findMany({
      where: { UniversityId: universityId },
      orderBy: { CreatedAt: 'desc' }
    });

    return dropboxes.map(dropbox => ({
      tslsToUniDropboxId: dropbox.TslsToUniDropboxId,
      tslsToUniDropboxName: dropbox.TslsToUniDropboxName,
      openingDate: dropbox.OpeningDate,
      closingDate: dropbox.ClosingDate,
      isOpen: dropbox.IsOpenStatus,
      universityId: dropbox.UniversityId,
      createdAt: dropbox.CreatedAt,
      updatedAt: dropbox.UpdatedAt
    }));
  }

  async getUniversitiesWithDropboxesAsync(): Promise<any[]> {
    const universities = await this.prisma.university.findMany({
      where: {
        IsActive: true,
        TslsToUniDropboxes: {
          some: {}
        }
      },
      include: {
        TslsToUniDropboxes: {
          orderBy: { CreatedAt: 'desc' }
        }
      }
    });

    return universities.map(uni => ({
      universityId: uni.UniversityId,
      universityName: uni.UniversityName,
      dropboxes: uni.TslsToUniDropboxes.map(dropbox => ({
        tslsToUniDropboxId: dropbox.TslsToUniDropboxId,
        tslsToUniDropboxName: dropbox.TslsToUniDropboxName,
        openingDate: dropbox.OpeningDate,
        closingDate: dropbox.ClosingDate,
        isOpen: dropbox.IsOpenStatus,
        createdAt: dropbox.CreatedAt,
        updatedAt: dropbox.UpdatedAt || undefined
      }))
    }));
  }
}

class InvalidOperationException extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'InvalidOperationException';
  }
}
