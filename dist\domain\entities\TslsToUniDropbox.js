export class TslsToUniDropbox {
    tslsToUniDropboxId;
    tslsToUniDropboxName;
    universityId;
    year;
    modeOfStudy;
    term;
    batchNumber;
    openingDate;
    closingDate;
    isOpenStatus;
    createdAt;
    updatedAt;
    constructor(tslsToUniDropboxId, tslsToUniDropboxName, universityId, year, modeOfStudy, term, batchNumber, openingDate, closingDate, isOpenStatus, createdAt = new Date(), updatedAt) {
        this.tslsToUniDropboxId = tslsToUniDropboxId;
        this.tslsToUniDropboxName = tslsToUniDropboxName;
        this.universityId = universityId;
        this.year = year;
        this.modeOfStudy = modeOfStudy;
        this.term = term;
        this.batchNumber = batchNumber;
        this.openingDate = openingDate;
        this.closingDate = closingDate;
        this.isOpenStatus = isOpenStatus;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }
    // Domain methods
    open() {
        return new TslsToUniDropbox(this.tslsToUniDropboxId, this.tslsToUniDropboxName, this.universityId, this.year, this.modeOfStudy, this.term, this.batchNumber, this.openingDate, this.closingDate, true, this.createdAt, new Date());
    }
    close() {
        return new TslsToUniDropbox(this.tslsToUniDropboxId, this.tslsToUniDropboxName, this.universityId, this.year, this.modeOfStudy, this.term, this.batchNumber, this.openingDate, this.closingDate, false, this.createdAt, new Date());
    }
    updateDates(openingDate, closingDate) {
        return new TslsToUniDropbox(this.tslsToUniDropboxId, this.tslsToUniDropboxName, this.universityId, this.year, this.modeOfStudy, this.term, this.batchNumber, openingDate, closingDate, this.isOpenStatus, this.createdAt, new Date());
    }
    // Business rules
    isCurrentlyOpen() {
        const now = new Date();
        return this.isOpenStatus &&
            now >= this.openingDate &&
            now <= this.closingDate;
    }
    canAcceptSubmissions() {
        return this.isCurrentlyOpen();
    }
    isValidDateRange() {
        return this.openingDate < this.closingDate;
    }
    isValidYear() {
        const currentYear = new Date().getFullYear();
        return this.year >= 2000 && this.year <= currentYear + 5;
    }
    isValidTerm() {
        return this.term >= 1 && this.term <= 4;
    }
    getDurationInDays() {
        const diffTime = Math.abs(this.closingDate.getTime() - this.openingDate.getTime());
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
}
