// Domain Entity for PTEIUser
import { UserType } from './enums.js';

export class PTEIUser {
  constructor(
    public readonly userId: string,
    public readonly email: string,
    private _hashedPassword: string,
    public readonly firstName: string = '',
    public readonly lastName: string = '',
    public readonly userProfilePicPath: string = '',
    public readonly userType: UserType,
    public readonly accountStatus: string = '',
    public readonly isAccountLockedOut: boolean = false,
    public readonly failedLoginAttempts: number = 0,
    public readonly isUserLoggedIn: boolean = false,
    public readonly userCreatedAt: Date = new Date(),
    public readonly userModifiedAt?: Date,
    public readonly emailVerificationToken?: string,
    public readonly emailVerificationTokenExpiry?: Date,
    public readonly passwordResetToken?: string,
    public readonly passwordResetTokenExpiry?: Date,
    public readonly roleId?: string,
    public readonly departmentId?: string
  ) {}

  // Domain methods
  public updatePassword(newHashedPassword: string): PTEIUser {
    return new PTEIUser(
      this.userId,
      this.email,
      newHashedPassword,
      this.firstName,
      this.lastName,
      this.userProfilePicPath,
      this.userType,
      this.accountStatus,
      this.isAccountLockedOut,
      0, // Reset failed attempts
      this.isUserLoggedIn,
      this.userCreatedAt,
      new Date(), // Update modified date
      this.emailVerificationToken,
      this.emailVerificationTokenExpiry,
      this.passwordResetToken,
      this.passwordResetTokenExpiry,
      this.roleId,
      this.departmentId
    );
  }

  public lockAccount(): PTEIUser {
    return new PTEIUser(
      this.userId,
      this.email,
      this._hashedPassword,
      this.firstName,
      this.lastName,
      this.userProfilePicPath,
      this.userType,
      'LOCKED',
      true,
      this.failedLoginAttempts,
      false,
      this.userCreatedAt,
      new Date(),
      this.emailVerificationToken,
      this.emailVerificationTokenExpiry,
      this.passwordResetToken,
      this.passwordResetTokenExpiry,
      this.roleId,
      this.departmentId
    );
  }

  public incrementFailedLoginAttempts(): PTEIUser {
    const newFailedAttempts = this.failedLoginAttempts + 1;
    const shouldLock = newFailedAttempts >= 5;

    return new PTEIUser(
      this.userId,
      this.email,
      this._hashedPassword,
      this.firstName,
      this.lastName,
      this.userProfilePicPath,
      this.userType,
      shouldLock ? 'LOCKED' : this.accountStatus,
      shouldLock,
      newFailedAttempts,
      this.isUserLoggedIn,
      this.userCreatedAt,
      new Date(),
      this.emailVerificationToken,
      this.emailVerificationTokenExpiry,
      this.passwordResetToken,
      this.passwordResetTokenExpiry,
      this.roleId,
      this.departmentId
    );
  }

  public resetFailedLoginAttempts(): PTEIUser {
    return new PTEIUser(
      this.userId,
      this.email,
      this._hashedPassword,
      this.firstName,
      this.lastName,
      this.userProfilePicPath,
      this.userType,
      this.accountStatus,
      false, // Unlock account
      0, // Reset failed attempts
      this.isUserLoggedIn,
      this.userCreatedAt,
      new Date(),
      this.emailVerificationToken,
      this.emailVerificationTokenExpiry,
      this.passwordResetToken,
      this.passwordResetTokenExpiry,
      this.roleId,
      this.departmentId
    );
  }

  public setLoggedIn(isLoggedIn: boolean): PTEIUser {
    return new PTEIUser(
      this.userId,
      this.email,
      this._hashedPassword,
      this.firstName,
      this.lastName,
      this.userProfilePicPath,
      this.userType,
      this.accountStatus,
      this.isAccountLockedOut,
      this.failedLoginAttempts,
      isLoggedIn,
      this.userCreatedAt,
      new Date(),
      this.emailVerificationToken,
      this.emailVerificationTokenExpiry,
      this.passwordResetToken,
      this.passwordResetTokenExpiry,
      this.roleId,
      this.departmentId
    );
  }

  // Getters
  public get hashedPassword(): string {
    return this._hashedPassword;
  }

  public get fullName(): string {
    return `${this.firstName} ${this.lastName}`.trim();
  }

  public get isLocked(): boolean {
    return this.isAccountLockedOut || this.accountStatus === 'LOCKED';
  }

  public isValidEmail(): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(this.email);
  }
}
