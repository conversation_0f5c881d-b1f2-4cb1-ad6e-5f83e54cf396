// Domain Entity for UniToTslsReportSubmission
export class UniToTslsReportSubmission {
    uniToTslsReportSubmissionId;
    uniToTslsReportSubmissionName;
    uniToTslsReportSubmissionFilePath;
    submittedBy;
    uniToTslsDropboxId;
    submittedAt;
    notes;
    constructor(uniToTslsReportSubmissionId, uniToTslsReportSubmissionName, uniToTslsReportSubmissionFilePath, submittedBy, uniToTslsDropboxId, submittedAt = new Date(), notes = '') {
        this.uniToTslsReportSubmissionId = uniToTslsReportSubmissionId;
        this.uniToTslsReportSubmissionName = uniToTslsReportSubmissionName;
        this.uniToTslsReportSubmissionFilePath = uniToTslsReportSubmissionFilePath;
        this.submittedBy = submittedBy;
        this.uniToTslsDropboxId = uniToTslsDropboxId;
        this.submittedAt = submittedAt;
        this.notes = notes;
    }
    // Domain methods
    updateNotes(newNotes) {
        return new UniToTslsReportSubmission(this.uniToTslsReportSubmissionId, this.uniToTslsReportSubmissionName, this.uniToTslsReportSubmissionFilePath, this.submittedBy, this.uniToTslsDropboxId, this.submittedAt, newNotes);
    }
    // Business rules
    isValidFilePath() {
        return this.uniToTslsReportSubmissionFilePath.trim().length > 0 &&
            (this.uniToTslsReportSubmissionFilePath.endsWith('.pdf') ||
                this.uniToTslsReportSubmissionFilePath.endsWith('.doc') ||
                this.uniToTslsReportSubmissionFilePath.endsWith('.docx') ||
                this.uniToTslsReportSubmissionFilePath.endsWith('.xlsx') ||
                this.uniToTslsReportSubmissionFilePath.endsWith('.xls'));
    }
    isRecentSubmission(hoursThreshold = 24) {
        const now = new Date();
        const diffInHours = (now.getTime() - this.submittedAt.getTime()) / (1000 * 60 * 60);
        return diffInHours <= hoursThreshold;
    }
    getFileExtension() {
        const parts = this.uniToTslsReportSubmissionFilePath.split('.');
        return parts.length > 1 ? parts[parts.length - 1].toLowerCase() : '';
    }
    getFileName() {
        const parts = this.uniToTslsReportSubmissionFilePath.split('/');
        return parts[parts.length - 1];
    }
    hasNotes() {
        return this.notes.trim().length > 0;
    }
    getFileSizeFromPath() {
        // This would typically integrate with file system to get actual size
        // For now, return a placeholder
        return 'Unknown';
    }
}
