// Domain Entity for UniToTslsDropbox
export class UniToTslsDropbox {
    uniToTslsDropboxId;
    uniToTslsDropboxName;
    openingDate;
    closingDate;
    isOpen;
    createdAt;
    updatedAt;
    constructor(uniToTslsDropboxId, uniToTslsDropboxName, openingDate, closingDate, isOpen, createdAt = new Date(), updatedAt) {
        this.uniToTslsDropboxId = uniToTslsDropboxId;
        this.uniToTslsDropboxName = uniToTslsDropboxName;
        this.openingDate = openingDate;
        this.closingDate = closingDate;
        this.isOpen = isOpen;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }
    // Domain methods
    open() {
        return new UniToTslsDropbox(this.uniToTslsDropboxId, this.uniToTslsDropboxName, this.openingDate, this.closingDate, true, this.createdAt, new Date());
    }
    close() {
        return new UniToTslsDropbox(this.uniToTslsDropboxId, this.uniToTslsDropboxName, this.openingDate, this.closingDate, false, this.createdAt, new Date());
    }
    updateDates(openingDate, closingDate) {
        return new UniToTslsDropbox(this.uniToTslsDropboxId, this.uniToTslsDropboxName, openingDate, closingDate, this.isOpen, this.createdAt, new Date());
    }
    // Business rules
    isCurrentlyOpen() {
        const now = new Date();
        return this.isOpen &&
            now >= this.openingDate &&
            now <= this.closingDate;
    }
    canAcceptSubmissions() {
        return this.isCurrentlyOpen();
    }
    isValidDateRange() {
        return this.openingDate < this.closingDate;
    }
    getDurationInDays() {
        const diffTime = Math.abs(this.closingDate.getTime() - this.openingDate.getTime());
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
    getTimeUntilOpening() {
        const now = new Date();
        if (now >= this.openingDate) {
            return null; // Already opened or past opening
        }
        return this.openingDate.getTime() - now.getTime();
    }
    getTimeUntilClosing() {
        const now = new Date();
        if (now >= this.closingDate) {
            return null; // Already closed
        }
        return this.closingDate.getTime() - now.getTime();
    }
}
