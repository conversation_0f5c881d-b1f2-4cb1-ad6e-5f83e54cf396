// University DTOs
export interface UniversityDto {
  universityId: string;
  universityName: string;
  primaryEmail: string;
  ccEmails?: string;
  bccEmails?: string;
  contactPerson: string;
  phone: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateUniversityDto {
  universityName: string;
  primaryEmail: string;
  ccEmails?: string;
  bccEmails?: string;
  contactPerson: string;
  phone: string;
}

export interface UpdateUniversityDto {
  universityId: string;
  universityName?: string;
  primaryEmail?: string;
  ccEmails?: string;
  bccEmails?: string;
  contactPerson?: string;
  phone?: string;
}
