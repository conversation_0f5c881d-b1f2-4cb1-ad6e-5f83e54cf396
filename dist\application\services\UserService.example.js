import { PTEIUser } from '../../domain/entities/index.js';
import { UserMapper } from '../mapppers/UserMapper.js';
export class UserService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    // Create a new user using domain entity
    async createUser(email, hashedPassword, firstName, lastName, userType, roleId, departmentId) {
        // Create domain entity
        const domainUser = new PTEIUser(crypto.randomUUID(), // Generate new ID
        email, hashedPassword, firstName, lastName, '', // userProfilePicPath
        userType, 'ACTIVE', // accountStatus
        false, // isAccountLockedOut
        0, // failedLoginAttempts
        false, // isUserLoggedIn
        new Date(), // userCreatedAt
        undefined, // userModifiedAt
        undefined, // emailVerificationToken
        undefined, // emailVerificationTokenExpiry
        undefined, // passwordResetToken
        undefined, // passwordResetTokenExpiry
        roleId, departmentId);
        // Validate domain rules
        if (!domainUser.isValidEmail()) {
            throw new Error('Invalid email format');
        }
        // Convert to Prisma format and save
        const prismaData = UserMapper.toPrisma(domainUser);
        const savedUser = await this.prisma.pTEIUser.create({
            data: prismaData
        });
        // Return domain entity
        return UserMapper.toDomain(savedUser);
    }
    // Get user by ID
    async getUserById(userId) {
        const prismaUser = await this.prisma.pTEIUser.findUnique({
            where: { UserId: userId }
        });
        if (!prismaUser) {
            return null;
        }
        return UserMapper.toDomain(prismaUser);
    }
    // Update user password using domain logic
    async updateUserPassword(userId, newHashedPassword) {
        const existingUser = await this.getUserById(userId);
        if (!existingUser) {
            throw new Error('User not found');
        }
        // Use domain method to update password
        const updatedUser = existingUser.updatePassword(newHashedPassword);
        // Save to database
        const prismaData = UserMapper.toPrisma(updatedUser);
        const savedUser = await this.prisma.pTEIUser.update({
            where: { UserId: userId },
            data: prismaData
        });
        return UserMapper.toDomain(savedUser);
    }
    // Lock user account using domain logic
    async lockUserAccount(userId) {
        const existingUser = await this.getUserById(userId);
        if (!existingUser) {
            throw new Error('User not found');
        }
        // Use domain method to lock account
        const lockedUser = existingUser.lockAccount();
        // Save to database
        const prismaData = UserMapper.toPrisma(lockedUser);
        const savedUser = await this.prisma.pTEIUser.update({
            where: { UserId: userId },
            data: prismaData
        });
        return UserMapper.toDomain(savedUser);
    }
    // Get all active users
    async getActiveUsers() {
        const prismaUsers = await this.prisma.pTEIUser.findMany({
            where: {
                AccountStatus: 'ACTIVE',
                IsAccountLockedOut: false
            }
        });
        return UserMapper.toDomainArray(prismaUsers);
    }
}
