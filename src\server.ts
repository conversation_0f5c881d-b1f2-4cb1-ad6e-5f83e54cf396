// This file sets up and starts a Fastify server, registering routes and configuring middleware and environment variables.
// Import Fastify library for creating the HTTP server
import Fastify from "fastify";
// Import environment variables
import { env } from "./infrastructure/config/env.js";
// Import Prisma Plugin
import prismaPlugin from "./infrastructure/db/prismaPlugin.js";
// Define and export function to build and configure the Fastify server
export async function buildServer() {
  // Create a Fastify instance with logger configurations
  const app = Fastify({
    logger: {
      level: process.env.LOG_LEVEL || "info",
    },
  });
  // Register routes and middleware
  // Register Prisma
  app.register(prismaPlugin);
  // Register routes
  return app;
}
// Define and export function to start the Fastify server
export async function startServer() {
  // Build the Fastify server instance
  const app = await buildServer();
  try {
    // Start the server, listening on the port specified in env.PORT and host '0.0.0.0' for external access
    await app.listen({ port: Number(env.PORT), host: "0.0.0.0" });
    // Use the app's logger to log the server start information
    app.log.info(`Server listening on http://localhost:${env.PORT}`);
  } catch (error) {
    app.log.error(error);
    process.exit(1);
  }
}