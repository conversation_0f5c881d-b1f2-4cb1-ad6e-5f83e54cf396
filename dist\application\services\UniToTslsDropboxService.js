import { UniToTslsDropbox } from '../../domain/entities/index.js';
export class UniToTslsDropboxService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createAsync(userId, createDto) {
        // Check if dropbox already exists with same name
        const existingDropbox = await this.prisma.uniToTslsDropbox.findFirst({
            where: {
                UniToTslsDropboxName: createDto.uniToTslsDropboxName,
                IsOpen: true
            }
        });
        if (existingDropbox) {
            throw new InvalidOperationException('An active dropbox with this name already exists');
        }
        const dropbox = new UniToTslsDropbox(crypto.randomUUID(), createDto.uniToTslsDropboxName, createDto.openingDate, createDto.closingDate, true, new Date(), new Date());
        // Validate business rules
        if (!dropbox.isValidDateRange()) {
            throw new Error('Invalid date range: opening date must be before closing date');
        }
        const createdDropbox = await this.prisma.uniToTslsDropbox.create({
            data: {
                UniToTslsDropboxId: dropbox.uniToTslsDropboxId,
                UniToTslsDropboxName: dropbox.uniToTslsDropboxName,
                OpeningDate: dropbox.openingDate,
                ClosingDate: dropbox.closingDate,
                IsOpen: dropbox.isOpen,
                CreatedAt: dropbox.createdAt,
                UpdatedAt: dropbox.updatedAt
            }
        });
        return {
            uniToTslsDropboxId: createdDropbox.UniToTslsDropboxId,
            uniToTslsDropboxName: createdDropbox.UniToTslsDropboxName,
            openingDate: createdDropbox.OpeningDate,
            closingDate: createdDropbox.ClosingDate,
            isOpen: createdDropbox.IsOpen,
            createdAt: createdDropbox.CreatedAt,
            updatedAt: createdDropbox.UpdatedAt
        };
    }
    async updateAsync(userId, dropboxId, updateDto) {
        const existingDropbox = await this.prisma.uniToTslsDropbox.findUnique({
            where: { UniToTslsDropboxId: dropboxId }
        });
        if (!existingDropbox) {
            throw new Error('Dropbox not found');
        }
        const updatedDropbox = await this.prisma.uniToTslsDropbox.update({
            where: { UniToTslsDropboxId: dropboxId },
            data: {
                UniToTslsDropboxName: updateDto.uniToTslsDropboxName ?? existingDropbox.UniToTslsDropboxName,
                OpeningDate: updateDto.openingDate ?? existingDropbox.OpeningDate,
                ClosingDate: updateDto.closingDate ?? existingDropbox.ClosingDate,
                IsOpen: updateDto.isOpen ?? existingDropbox.IsOpen,
                UpdatedAt: new Date()
            }
        });
        return {
            uniToTslsDropboxId: updatedDropbox.UniToTslsDropboxId,
            uniToTslsDropboxName: updatedDropbox.UniToTslsDropboxName,
            openingDate: updatedDropbox.OpeningDate,
            closingDate: updatedDropbox.ClosingDate,
            isOpen: updatedDropbox.IsOpen,
            createdAt: updatedDropbox.CreatedAt,
            updatedAt: updatedDropbox.UpdatedAt
        };
    }
    async getByIdAsync(dropboxId) {
        const dropbox = await this.prisma.uniToTslsDropbox.findUnique({
            where: { UniToTslsDropboxId: dropboxId }
        });
        if (!dropbox) {
            throw new Error('Dropbox not found');
        }
        return {
            uniToTslsDropboxId: dropbox.UniToTslsDropboxId,
            uniToTslsDropboxName: dropbox.UniToTslsDropboxName,
            openingDate: dropbox.OpeningDate,
            closingDate: dropbox.ClosingDate,
            isOpen: dropbox.IsOpen,
            createdAt: dropbox.CreatedAt,
            updatedAt: dropbox.UpdatedAt
        };
    }
}
class InvalidOperationException extends Error {
    constructor(message) {
        super(message);
        this.name = 'InvalidOperationException';
    }
}
