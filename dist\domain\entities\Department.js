// Domain Entity for Department
export class Department {
    departmentId;
    departmentName;
    description;
    isActive;
    createdAt;
    constructor(departmentId, departmentName, description, isActive = true, createdAt = new Date()) {
        this.departmentId = departmentId;
        this.departmentName = departmentName;
        this.description = description;
        this.isActive = isActive;
        this.createdAt = createdAt;
    }
    // Domain methods
    deactivate() {
        return new Department(this.departmentId, this.departmentName, this.description, false, this.createdAt);
    }
    activate() {
        return new Department(this.departmentId, this.departmentName, this.description, true, this.createdAt);
    }
    updateDetails(newName, newDescription) {
        return new Department(this.departmentId, newName, newDescription, this.isActive, this.createdAt);
    }
    // Business rules
    canBeDeleted() {
        return !this.isActive;
    }
    isValidForAssignment() {
        return this.isActive && this.departmentName.trim().length > 0;
    }
}
