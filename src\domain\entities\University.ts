// Domain Entity for University
export class University {
  constructor(
    public readonly universityId: string,
    public readonly universityName: string,
    public readonly primaryEmail: string,
    public readonly contactPerson: string,
    public readonly phone: string,
    public readonly isActive: boolean = true,
    public readonly createdAt: Date = new Date(),
    public readonly updatedAt: Date = new Date(),
    public readonly ccEmails?: string,
    public readonly bccEmails?: string
  ) {}

  // Domain methods
  public deactivate(): University {
    return new University(
      this.universityId,
      this.universityName,
      this.primaryEmail,
      this.contactPerson,
      this.phone,
      false,
      this.createdAt,
      new Date(),
      this.ccEmails,
      this.bccEmails
    );
  }

  public activate(): University {
    return new University(
      this.universityId,
      this.universityName,
      this.primaryEmail,
      this.contactPerson,
      this.phone,
      true,
      this.createdAt,
      new Date(),
      this.ccEmails,
      this.bccEmails
    );
  }

  public updateContactInfo(
    primaryEmail: string,
    contactPerson: string,
    phone: string,
    ccEmails?: string,
    bccEmails?: string
  ): University {
    return new University(
      this.universityId,
      this.universityName,
      primaryEmail,
      contactPerson,
      phone,
      this.isActive,
      this.createdAt,
      new Date(),
      ccEmails,
      bccEmails
    );
  }

  // Business rules
  public isValidForOperations(): boolean {
    return this.isActive && this.universityName.trim().length > 0;
  }

  public isValidEmail(): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(this.primaryEmail);
  }

  public hasValidContactInfo(): boolean {
    return this.contactPerson.trim().length > 0 && 
           this.phone.trim().length > 0 && 
           this.isValidEmail();
  }

  public getEmailList(): string[] {
    const emails = [this.primaryEmail];
    
    if (this.ccEmails) {
      emails.push(...this.ccEmails.split(',').map(email => email.trim()));
    }
    
    if (this.bccEmails) {
      emails.push(...this.bccEmails.split(',').map(email => email.trim()));
    }
    
    return emails.filter(email => email.length > 0);
  }
}
