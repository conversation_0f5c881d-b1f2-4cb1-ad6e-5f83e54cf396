// Domain Entity for UniToTslsDropbox
export class UniToTslsDropbox {
  constructor(
    public readonly uniToTslsDropboxId: string,
    public readonly uniToTslsDropboxName: string,
    public readonly openingDate: Date,
    public readonly closingDate: Date,
    public readonly isOpen: boolean,
    public readonly createdAt: Date = new Date(),
    public readonly updatedAt?: Date
  ) {}

  // Domain methods
  public open(): UniToTslsDropbox {
    return new UniToTslsDropbox(
      this.uniToTslsDropboxId,
      this.uniToTslsDropboxName,
      this.openingDate,
      this.closingDate,
      true,
      this.createdAt,
      new Date()
    );
  }

  public close(): UniToTslsDropbox {
    return new UniToTslsDropbox(
      this.uniToTslsDropboxId,
      this.uniToTslsDropboxName,
      this.openingDate,
      this.closingDate,
      false,
      this.createdAt,
      new Date()
    );
  }

  public updateDates(openingDate: Date, closingDate: Date): UniToTslsDropbox {
    return new UniToTslsDropbox(
      this.uniToTslsDropboxId,
      this.uniToTslsDropboxName,
      openingDate,
      closingDate,
      this.isOpen,
      this.createdAt,
      new Date()
    );
  }

  // Business rules
  public isCurrentlyOpen(): boolean {
    const now = new Date();
    return this.isOpen && 
           now >= this.openingDate && 
           now <= this.closingDate;
  }

  public canAcceptSubmissions(): boolean {
    return this.isCurrentlyOpen();
  }

  public isValidDateRange(): boolean {
    return this.openingDate < this.closingDate;
  }

  public getDurationInDays(): number {
    const diffTime = Math.abs(this.closingDate.getTime() - this.openingDate.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  public getTimeUntilOpening(): number | null {
    const now = new Date();
    if (now >= this.openingDate) {
      return null; // Already opened or past opening
    }
    return this.openingDate.getTime() - now.getTime();
  }

  public getTimeUntilClosing(): number | null {
    const now = new Date();
    if (now >= this.closingDate) {
      return null; // Already closed
    }
    return this.closingDate.getTime() - now.getTime();
  }
}
