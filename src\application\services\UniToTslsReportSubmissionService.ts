// UniToTslsReportSubmission Service
import { PrismaClient } from '../../generated/prisma/index.js';
import { UniToTslsReportSubmission } from '../../domain/entities/index.js';
import { UniToTslsReportSubmissionDto, UniToTslsReportSubmissionCreateDto } from '../dto/dropbox.dto.js';
import * as fs from 'fs';
import * as path from 'path';

export class UniToTslsReportSubmissionService {
  constructor(private prisma: PrismaClient) {}

  async createAsync(userId: string, createDto: UniToTslsReportSubmissionCreateDto): Promise<UniToTslsReportSubmissionDto> {
    // Check if dropbox exists and is open
    const dropbox = await this.prisma.uniToTslsDropbox.findUnique({
      where: { UniToTslsDropboxId: createDto.uniToTslsDropboxId }
    });

    if (!dropbox) {
      throw new Error('Dropbox not found');
    }

    if (!dropbox.IsOpen) {
      throw new Error('Dropbox is not open for submissions');
    }

    // Check if dropbox is currently accepting submissions (within date range)
    const now = new Date();
    if (now < dropbox.OpeningDate || now > dropbox.ClosingDate) {
      throw new Error('Dropbox is not currently accepting submissions');
    }

    // Generate unique filename and save file
    const fileExtension = this.getFileExtension(createDto.file);
    const fileName = `${crypto.randomUUID()}${fileExtension}`;
    const uploadsDir = path.join(process.cwd(), 'uploads', 'uni-to-tsls-reports');
    
    // Ensure uploads directory exists
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }

    const filePath = path.join(uploadsDir, fileName);
    
    // Save file (this is a simplified version - in production you'd handle this properly)
    if (Buffer.isBuffer(createDto.file)) {
      fs.writeFileSync(filePath, createDto.file);
    } else {
      // Handle File object (would need proper multipart handling in production)
      throw new Error('File handling not implemented for File objects');
    }

    const submission = new UniToTslsReportSubmission(
      crypto.randomUUID(),
      fileName,
      filePath,
      userId,
      createDto.uniToTslsDropboxId,
      new Date(),
      ''
    );

    // Validate business rules
    if (!submission.isValidFilePath()) {
      // Clean up uploaded file
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
      throw new Error('Invalid file type. Only PDF, DOC, DOCX, XLS, and XLSX files are allowed');
    }

    const createdSubmission = await this.prisma.uniToTslsReportSubmission.create({
      data: {
        UniToTslsReportSubmissionId: submission.uniToTslsReportSubmissionId,
        UniToTslsReportSubmissionName: fileName,
        UniToTslsReportSubmissionFilePath: submission.uniToTslsReportSubmissionFilePath,
        SubmittedAt: submission.submittedAt,
        Notes: '',
        SubmittedBy: userId,
        UniToTslsDropboxId: submission.uniToTslsDropboxId
      }
    });

    return {
      uniToTslsReportSubmissionId: createdSubmission.UniToTslsReportSubmissionId,
      uniToTslsReportSubmissionFilePath: createdSubmission.UniToTslsReportSubmissionFilePath,
      submittedAt: createdSubmission.SubmittedAt,
      uniToTslsDropboxId: createdSubmission.UniToTslsDropboxId
    };
  }

  private getFileExtension(file: File | Buffer): string {
    if (Buffer.isBuffer(file)) {
      // In a real implementation, you'd detect file type from buffer
      return '.pdf'; // Default extension
    } else {
      // For File objects
      const name = (file as any).name || '';
      const lastDot = name.lastIndexOf('.');
      return lastDot > -1 ? name.substring(lastDot) : '.pdf';
    }
  }
}
