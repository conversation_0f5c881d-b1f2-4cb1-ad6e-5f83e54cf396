import { UniToTslsDropboxService } from '../../../application/services/UniToTslsDropboxService.js';
export default async function uniToTslsDropboxRoutes(fastify) {
    const dropboxService = new UniToTslsDropboxService(fastify.prisma);
    // Create Uni to TSLS dropbox
    fastify.post('/create-uni-to-tsls-dropbox', async (request, reply) => {
        try {
            const createDto = request.body;
            const { userId } = request.query;
            if (!createDto.uniToTslsDropboxName || !createDto.openingDate || !createDto.closingDate) {
                return reply.status(400).send({ error: 'Missing required fields' });
            }
            const result = await dropboxService.createAsync(userId, createDto);
            return reply.status(201).send(result);
        }
        catch (error) {
            const message = error instanceof Error ? error.message : 'An error occurred';
            if (message.includes('already exists')) {
                return reply.status(409).send({ error: message });
            }
            return reply.status(500).send({ error: 'An error occurred while creating the dropbox.' });
        }
    });
    // Update Uni to TSLS dropbox
    fastify.put('/update-uni-to-tsls-dropbox/:dropboxId', async (request, reply) => {
        try {
            const { dropboxId } = request.params;
            const updateDto = request.body;
            const { userId } = request.query;
            const result = await dropboxService.updateAsync(userId, dropboxId, updateDto);
            return reply.status(200).send(result);
        }
        catch (error) {
            const message = error instanceof Error ? error.message : 'An error occurred';
            return reply.status(500).send({ error: message });
        }
    });
    // Get individual Uni to TSLS dropbox
    fastify.get('/get-individual-uni-to-tsls-dropbox/:dropboxId', async (request, reply) => {
        try {
            const { dropboxId } = request.params;
            const result = await dropboxService.getByIdAsync(dropboxId);
            return reply.status(200).send(result);
        }
        catch (error) {
            const message = error instanceof Error ? error.message : 'An error occurred';
            return reply.status(500).send({ error: message });
        }
    });
}
