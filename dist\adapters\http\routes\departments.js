import { DepartmentService } from '../../../application/services/DepartmentService.js';
import { verifyJWT } from '../middleware/auth.js';
export default async function departmentRoutes(fastify) {
    const departmentService = new DepartmentService(fastify.prisma);
    // All routes require authentication
    fastify.addHook('preHandler', verifyJWT);
    // Get all departments
    fastify.get('/get-all-departments', async (request, reply) => {
        try {
            const departments = await departmentService.getAllDepartments();
            return reply.status(200).send(departments);
        }
        catch (error) {
            return reply.status(500).send({ error: 'Internal server error' });
        }
    });
    // Get department by ID
    fastify.get('/get-department-by-id/:id', async (request, reply) => {
        try {
            const { id } = request.params;
            const department = await departmentService.getDepartmentById(id);
            if (!department) {
                return reply.status(404).send({ error: 'Department not found' });
            }
            return reply.status(200).send(department);
        }
        catch (error) {
            return reply.status(500).send({ error: 'Internal server error' });
        }
    });
    // Create new department
    fastify.post('/create-new-department', async (request, reply) => {
        try {
            const createDto = request.body;
            if (!createDto.departmentName) {
                return reply.status(400).send({ error: 'Department name is required' });
            }
            const createdDepartment = await departmentService.createDepartment(createDto);
            return reply.status(201).send(createdDepartment);
        }
        catch (error) {
            return reply.status(500).send({ error: 'Internal server error' });
        }
    });
    // Update department
    fastify.put('/update-department/:id', async (request, reply) => {
        try {
            const { id } = request.params;
            const updateDto = request.body;
            const updatedDepartment = await departmentService.updateDepartment(id, updateDto);
            if (!updatedDepartment) {
                return reply.status(404).send({ error: 'Department not found' });
            }
            return reply.status(204).send();
        }
        catch (error) {
            return reply.status(500).send({ error: 'Internal server error' });
        }
    });
    // Delete department
    fastify.delete('/delete-department/:id', async (request, reply) => {
        try {
            const { id } = request.params;
            await departmentService.deleteDepartment(id);
            return reply.status(204).send();
        }
        catch (error) {
            return reply.status(500).send({ error: 'Internal server error' });
        }
    });
}
