import { RoleService } from '../../../application/services/RoleService.js';
import { verifyJWT } from '../middleware/auth.js';
export default async function roleRoutes(fastify) {
    const roleService = new RoleService(fastify.prisma);
    // All routes require authentication
    fastify.addHook('preHandler', verifyJWT);
    // Get all roles
    fastify.get('/get-all-roles', async (request, reply) => {
        try {
            const roles = await roleService.getAllRoles();
            return reply.status(200).send(roles);
        }
        catch (error) {
            return reply.status(500).send({ error: 'Internal server error' });
        }
    });
    // Get role by ID
    fastify.get('/get-role-by-id/:id', async (request, reply) => {
        try {
            const { id } = request.params;
            const role = await roleService.getRoleById(id);
            if (!role) {
                return reply.status(404).send({ error: 'Role not found' });
            }
            return reply.status(200).send(role);
        }
        catch (error) {
            return reply.status(500).send({ error: 'Internal server error' });
        }
    });
    // Create new role
    fastify.post('/', async (request, reply) => {
        try {
            const createDto = request.body;
            if (!createDto.roleName) {
                return reply.status(400).send({ error: 'Role name is required' });
            }
            const createdRole = await roleService.createRole(createDto);
            return reply.status(201).send(createdRole);
        }
        catch (error) {
            return reply.status(500).send({ error: 'Internal server error' });
        }
    });
    // Update role
    fastify.put('/:id', async (request, reply) => {
        try {
            const { id } = request.params;
            const updateDto = request.body;
            const updatedRole = await roleService.updateRole(id, updateDto);
            if (!updatedRole) {
                return reply.status(404).send({ error: 'Role not found' });
            }
            return reply.status(204).send();
        }
        catch (error) {
            return reply.status(500).send({ error: 'Internal server error' });
        }
    });
    // Delete role
    fastify.delete('/:id', async (request, reply) => {
        try {
            const { id } = request.params;
            await roleService.deleteRole(id);
            return reply.status(204).send();
        }
        catch (error) {
            return reply.status(500).send({ error: 'Internal server error' });
        }
    });
}
