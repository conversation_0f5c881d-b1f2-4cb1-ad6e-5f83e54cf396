// Authentication Controller
import { FastifyRequest, FastifyReply } from 'fastify';
import { AuthService, IPasswordHasher, IAuditService } from '../../../application/services/AuthService.js';
import { LoginDto } from '../../../application/dto/auth.dto.js';

// Simple password hasher implementation (in production, use bcrypt)
class SimplePasswordHasher implements IPasswordHasher {
  hash(password: string): string {
    // In production, use proper hashing like bcrypt
    return Buffer.from(password).toString('base64');
  }

  verify(password: string, hash: string): boolean {
    return this.hash(password) === hash;
  }
}

// Simple audit service implementation
class SimpleAuditService implements IAuditService {
  constructor(private prisma: any) {}

  async logAsync(userId: string, actionType: string, tableName: string, recordId: string, oldValues?: string, newValues?: string, additionalInfo?: string): Promise<void> {
    await this.prisma.auditLog.create({
      data: {
        LogId: crypto.randomUUID(),
        ActionType: actionType,
        TableName: tableName,
        RecordId: recordId,
        OldValues: oldValues,
        NewValues: newValues,
        IPAddress: '127.0.0.1', // In production, get from request
        SessionId: crypto.randomUUID(),
        UserAgent: 'API',
        AdditionalInfo: additionalInfo,
        UserId: userId
      }
    });
  }
}

export class AuthController {
  private authService: AuthService;

  constructor(prisma: any) {
    const passwordHasher = new SimplePasswordHasher();
    const auditService = new SimpleAuditService(prisma);
    this.authService = new AuthService(prisma, passwordHasher, auditService);
  }

  async login(request: FastifyRequest<{ Body: LoginDto }>, reply: FastifyReply) {
    try {
      const { email, password } = request.body;

      if (!email || !password) {
        return reply.status(400).send({ error: 'Invalid input.' });
      }

      const result = await this.authService.login({ email, password });
      return reply.status(200).send(result);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';
      
      if (message.includes('Invalid credentials') || message.includes('locked') || message.includes('not active')) {
        return reply.status(401).send({ error: message });
      }
      
      return reply.status(500).send({ error: 'Internal server error' });
    }
  }

  async logout(request: FastifyRequest, reply: FastifyReply) {
    try {
      const userId = request.user!.userId;
      await this.authService.logout(userId);
      return reply.status(200).send({ message: 'Logged out' });
    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';
      
      if (message.includes('not found')) {
        return reply.status(404).send({ error: message });
      }
      
      return reply.status(500).send({ error: 'Internal server error' });
    }
  }
}
