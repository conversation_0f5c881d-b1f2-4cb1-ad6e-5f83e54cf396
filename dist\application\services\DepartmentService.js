import { Department } from '../../domain/entities/index.js';
import { DepartmentMapper } from '../mapppers/DepartmentMapper.js';
export class DepartmentService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getAllDepartments() {
        const prismaDepartments = await this.prisma.department.findMany({
            where: { IsActive: true },
            orderBy: { DepartmentName: 'asc' }
        });
        return prismaDepartments.map(dept => ({
            departmentId: dept.DepartmentId,
            departmentName: dept.DepartmentName,
            description: dept.Description || undefined,
            isActive: dept.IsActive,
            createdAt: dept.CreatedAt
        }));
    }
    async getDepartmentById(id) {
        const prismaDepartment = await this.prisma.department.findUnique({
            where: { DepartmentId: id }
        });
        if (!prismaDepartment) {
            return null;
        }
        return {
            departmentId: prismaDepartment.DepartmentId,
            departmentName: prismaDepartment.DepartmentName,
            description: prismaDepartment.Description || undefined,
            isActive: prismaDepartment.IsActive,
            createdAt: prismaDepartment.CreatedAt
        };
    }
    async createDepartment(createDto) {
        const department = new Department(crypto.randomUUID(), createDto.departmentName, createDto.description, true, new Date());
        const prismaData = DepartmentMapper.toPrisma(department);
        const createdDepartment = await this.prisma.department.create({
            data: prismaData
        });
        return {
            departmentId: createdDepartment.DepartmentId,
            departmentName: createdDepartment.DepartmentName,
            description: createdDepartment.Description || undefined,
            isActive: createdDepartment.IsActive,
            createdAt: createdDepartment.CreatedAt
        };
    }
    async updateDepartment(id, updateDto) {
        const existingDepartment = await this.prisma.department.findUnique({
            where: { DepartmentId: id }
        });
        if (!existingDepartment) {
            return null;
        }
        const updatedDepartment = await this.prisma.department.update({
            where: { DepartmentId: id },
            data: {
                DepartmentName: updateDto.departmentName ?? existingDepartment.DepartmentName,
                Description: updateDto.description ?? existingDepartment.Description,
                IsActive: updateDto.isActive ?? existingDepartment.IsActive
            }
        });
        return {
            departmentId: updatedDepartment.DepartmentId,
            departmentName: updatedDepartment.DepartmentName,
            description: updatedDepartment.Description || undefined,
            isActive: updatedDepartment.IsActive,
            createdAt: updatedDepartment.CreatedAt
        };
    }
    async deleteDepartment(id) {
        // Soft delete by setting IsActive to false
        await this.prisma.department.update({
            where: { DepartmentId: id },
            data: { IsActive: false }
        });
    }
}
