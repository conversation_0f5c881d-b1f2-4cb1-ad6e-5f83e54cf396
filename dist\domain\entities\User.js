export class PTEIUser {
    userId;
    email;
    _hashedPassword;
    firstName;
    lastName;
    userProfilePicPath;
    userType;
    accountStatus;
    isAccountLockedOut;
    failedLoginAttempts;
    isUserLoggedIn;
    userCreatedAt;
    userModifiedAt;
    emailVerificationToken;
    emailVerificationTokenExpiry;
    passwordResetToken;
    passwordResetTokenExpiry;
    roleId;
    departmentId;
    constructor(userId, email, _hashedPassword, firstName = '', lastName = '', userProfilePicPath = '', userType, accountStatus = '', isAccountLockedOut = false, failedLoginAttempts = 0, isUserLoggedIn = false, userCreatedAt = new Date(), userModifiedAt, emailVerificationToken, emailVerificationTokenExpiry, passwordResetToken, passwordResetTokenExpiry, roleId, departmentId) {
        this.userId = userId;
        this.email = email;
        this._hashedPassword = _hashedPassword;
        this.firstName = firstName;
        this.lastName = lastName;
        this.userProfilePicPath = userProfilePicPath;
        this.userType = userType;
        this.accountStatus = accountStatus;
        this.isAccountLockedOut = isAccountLockedOut;
        this.failedLoginAttempts = failedLoginAttempts;
        this.isUserLoggedIn = isUserLoggedIn;
        this.userCreatedAt = userCreatedAt;
        this.userModifiedAt = userModifiedAt;
        this.emailVerificationToken = emailVerificationToken;
        this.emailVerificationTokenExpiry = emailVerificationTokenExpiry;
        this.passwordResetToken = passwordResetToken;
        this.passwordResetTokenExpiry = passwordResetTokenExpiry;
        this.roleId = roleId;
        this.departmentId = departmentId;
    }
    // Domain methods
    updatePassword(newHashedPassword) {
        return new PTEIUser(this.userId, this.email, newHashedPassword, this.firstName, this.lastName, this.userProfilePicPath, this.userType, this.accountStatus, this.isAccountLockedOut, 0, // Reset failed attempts
        this.isUserLoggedIn, this.userCreatedAt, new Date(), // Update modified date
        this.emailVerificationToken, this.emailVerificationTokenExpiry, this.passwordResetToken, this.passwordResetTokenExpiry, this.roleId, this.departmentId);
    }
    lockAccount() {
        return new PTEIUser(this.userId, this.email, this._hashedPassword, this.firstName, this.lastName, this.userProfilePicPath, this.userType, 'LOCKED', true, this.failedLoginAttempts, false, this.userCreatedAt, new Date(), this.emailVerificationToken, this.emailVerificationTokenExpiry, this.passwordResetToken, this.passwordResetTokenExpiry, this.roleId, this.departmentId);
    }
    incrementFailedLoginAttempts() {
        const newFailedAttempts = this.failedLoginAttempts + 1;
        const shouldLock = newFailedAttempts >= 5;
        return new PTEIUser(this.userId, this.email, this._hashedPassword, this.firstName, this.lastName, this.userProfilePicPath, this.userType, shouldLock ? 'LOCKED' : this.accountStatus, shouldLock, newFailedAttempts, this.isUserLoggedIn, this.userCreatedAt, new Date(), this.emailVerificationToken, this.emailVerificationTokenExpiry, this.passwordResetToken, this.passwordResetTokenExpiry, this.roleId, this.departmentId);
    }
    resetFailedLoginAttempts() {
        return new PTEIUser(this.userId, this.email, this._hashedPassword, this.firstName, this.lastName, this.userProfilePicPath, this.userType, this.accountStatus, false, // Unlock account
        0, // Reset failed attempts
        this.isUserLoggedIn, this.userCreatedAt, new Date(), this.emailVerificationToken, this.emailVerificationTokenExpiry, this.passwordResetToken, this.passwordResetTokenExpiry, this.roleId, this.departmentId);
    }
    setLoggedIn(isLoggedIn) {
        return new PTEIUser(this.userId, this.email, this._hashedPassword, this.firstName, this.lastName, this.userProfilePicPath, this.userType, this.accountStatus, this.isAccountLockedOut, this.failedLoginAttempts, isLoggedIn, this.userCreatedAt, new Date(), this.emailVerificationToken, this.emailVerificationTokenExpiry, this.passwordResetToken, this.passwordResetTokenExpiry, this.roleId, this.departmentId);
    }
    // Getters
    get hashedPassword() {
        return this._hashedPassword;
    }
    get fullName() {
        return `${this.firstName} ${this.lastName}`.trim();
    }
    get isLocked() {
        return this.isAccountLockedOut || this.accountStatus === 'LOCKED';
    }
    isValidEmail() {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(this.email);
    }
}
