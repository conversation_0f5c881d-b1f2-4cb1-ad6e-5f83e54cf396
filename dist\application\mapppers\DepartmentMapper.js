import { Department } from '../../domain/entities/index.js';
export class DepartmentMapper {
    // Convert from Prisma model to Domain entity
    static toDomain(prismaDepartment) {
        return new Department(prismaDepartment.DepartmentId, prismaDepartment.DepartmentName, prismaDepartment.Description || undefined, prismaDepartment.IsActive, prismaDepartment.CreatedAt);
    }
    // Convert from Domain entity to Prisma format
    static toPrisma(department) {
        return {
            DepartmentId: department.departmentId,
            DepartmentName: department.departmentName,
            Description: department.description || null,
            IsActive: department.isActive,
            CreatedAt: department.createdAt
        };
    }
}
