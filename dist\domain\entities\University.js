// Domain Entity for University
export class University {
    universityId;
    universityName;
    primaryEmail;
    contactPerson;
    phone;
    isActive;
    createdAt;
    updatedAt;
    ccEmails;
    bccEmails;
    constructor(universityId, universityName, primaryEmail, contactPerson, phone, isActive = true, createdAt = new Date(), updatedAt = new Date(), ccEmails, bccEmails) {
        this.universityId = universityId;
        this.universityName = universityName;
        this.primaryEmail = primaryEmail;
        this.contactPerson = contactPerson;
        this.phone = phone;
        this.isActive = isActive;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.ccEmails = ccEmails;
        this.bccEmails = bccEmails;
    }
    // Domain methods
    deactivate() {
        return new University(this.universityId, this.universityName, this.primaryEmail, this.contactPerson, this.phone, false, this.createdAt, new Date(), this.ccEmails, this.bccEmails);
    }
    activate() {
        return new University(this.universityId, this.universityName, this.primaryEmail, this.contactPerson, this.phone, true, this.createdAt, new Date(), this.ccEmails, this.bccEmails);
    }
    updateContactInfo(primaryEmail, contactPerson, phone, ccEmails, bccEmails) {
        return new University(this.universityId, this.universityName, primaryEmail, contactPerson, phone, this.isActive, this.createdAt, new Date(), ccEmails, bccEmails);
    }
    // Business rules
    isValidForOperations() {
        return this.isActive && this.universityName.trim().length > 0;
    }
    isValidEmail() {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(this.primaryEmail);
    }
    hasValidContactInfo() {
        return this.contactPerson.trim().length > 0 &&
            this.phone.trim().length > 0 &&
            this.isValidEmail();
    }
    getEmailList() {
        const emails = [this.primaryEmail];
        if (this.ccEmails) {
            emails.push(...this.ccEmails.split(',').map(email => email.trim()));
        }
        if (this.bccEmails) {
            emails.push(...this.bccEmails.split(',').map(email => email.trim()));
        }
        return emails.filter(email => email.length > 0);
    }
}
