// Domain Entity for Role
export class Role {
  constructor(
    public readonly roleId: string,
    public readonly roleName: string,
    public readonly description?: string,
    public readonly isActive: boolean = true,
    public readonly createdAt: Date = new Date()
  ) {}

  // Domain methods
  public deactivate(): Role {
    return new Role(
      this.roleId,
      this.roleName,
      this.description,
      false,
      this.createdAt
    );
  }

  public activate(): Role {
    return new Role(
      this.roleId,
      this.roleName,
      this.description,
      true,
      this.createdAt
    );
  }

  public updateDescription(newDescription: string): Role {
    return new Role(
      this.roleId,
      this.roleName,
      newDescription,
      this.isActive,
      this.createdAt
    );
  }

  // Business rules
  public canBeDeleted(): boolean {
    return !this.isActive;
  }

  public isValidForAssignment(): boolean {
    return this.isActive && this.roleName.trim().length > 0;
  }
}
