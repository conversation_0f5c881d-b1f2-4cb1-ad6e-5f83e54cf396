// UniToTslsDropbox Controller
import { FastifyRequest, FastifyReply } from 'fastify';
import { UniToTslsDropboxService } from '../../../application/services/UniToTslsDropboxService.js';
import { UniToTslsDropboxCreateDto, UniToTslsDropboxUpdateDto } from '../../../application/dto/dropbox.dto.js';

export class UniToTslsDropboxController {
  private dropboxService: UniToTslsDropboxService;

  constructor(prisma: any) {
    this.dropboxService = new UniToTslsDropboxService(prisma);
  }

  async createUniToTslsDropbox(request: FastifyRequest<{ Body: UniToTslsDropboxCreateDto; Querystring: { userId: string } }>, reply: FastifyReply) {
    try {
      const createDto = request.body;
      const { userId } = request.query;
      
      if (!createDto.uniToTslsDropboxName || !createDto.openingDate || !createDto.closingDate) {
        return reply.status(400).send({ error: 'Missing required fields' });
      }
      
      const result = await this.dropboxService.createAsync(userId, createDto);
      return reply.status(201).send(result);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';
      
      if (message.includes('already exists')) {
        return reply.status(409).send({ error: message });
      }
      
      return reply.status(500).send({ error: 'An error occurred while creating the dropbox.' });
    }
  }

  async updateUniToTslsDropbox(request: FastifyRequest<{ 
    Params: { dropboxId: string }; 
    Body: UniToTslsDropboxUpdateDto; 
    Querystring: { userId: string } 
  }>, reply: FastifyReply) {
    try {
      const { dropboxId } = request.params;
      const updateDto = request.body;
      const { userId } = request.query;
      
      const result = await this.dropboxService.updateAsync(userId, dropboxId, updateDto);
      return reply.status(200).send(result);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';
      return reply.status(500).send({ error: message });
    }
  }

  async getIndividualUniToTslsDropbox(request: FastifyRequest<{ Params: { dropboxId: string } }>, reply: FastifyReply) {
    try {
      const { dropboxId } = request.params;
      const result = await this.dropboxService.getByIdAsync(dropboxId);
      return reply.status(200).send(result);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';
      return reply.status(500).send({ error: message });
    }
  }
}
