// Domain Entity for TslsToUniDropbox
import { ModeOfStudy } from './enums.js';

export class TslsToUniDropbox {
  constructor(
    public readonly tslsToUniDropboxId: string,
    public readonly tslsToUniDropboxName: string,
    public readonly universityId: string,
    public readonly year: number,
    public readonly modeOfStudy: ModeOfStudy,
    public readonly term: number,
    public readonly batchNumber: number,
    public readonly openingDate: Date,
    public readonly closingDate: Date,
    public readonly isOpenStatus: boolean,
    public readonly createdAt: Date = new Date(),
    public readonly updatedAt?: Date
  ) {}

  // Domain methods
  public open(): TslsToUniDropbox {
    return new TslsToUniDropbox(
      this.tslsToUniDropboxId,
      this.tslsToUniDropboxName,
      this.universityId,
      this.year,
      this.modeOfStudy,
      this.term,
      this.batchNumber,
      this.openingDate,
      this.closingDate,
      true,
      this.createdAt,
      new Date()
    );
  }

  public close(): TslsToUniDropbox {
    return new TslsToUniDropbox(
      this.tslsToUniDropboxId,
      this.tslsToUniDropboxName,
      this.universityId,
      this.year,
      this.modeOfStudy,
      this.term,
      this.batchNumber,
      this.openingDate,
      this.closingDate,
      false,
      this.createdAt,
      new Date()
    );
  }

  public updateDates(openingDate: Date, closingDate: Date): TslsToUniDropbox {
    return new TslsToUniDropbox(
      this.tslsToUniDropboxId,
      this.tslsToUniDropboxName,
      this.universityId,
      this.year,
      this.modeOfStudy,
      this.term,
      this.batchNumber,
      openingDate,
      closingDate,
      this.isOpenStatus,
      this.createdAt,
      new Date()
    );
  }

  // Business rules
  public isCurrentlyOpen(): boolean {
    const now = new Date();
    return this.isOpenStatus && 
           now >= this.openingDate && 
           now <= this.closingDate;
  }

  public canAcceptSubmissions(): boolean {
    return this.isCurrentlyOpen();
  }

  public isValidDateRange(): boolean {
    return this.openingDate < this.closingDate;
  }

  public isValidYear(): boolean {
    const currentYear = new Date().getFullYear();
    return this.year >= 2000 && this.year <= currentYear + 5;
  }

  public isValidTerm(): boolean {
    return this.term >= 1 && this.term <= 4;
  }

  public getDurationInDays(): number {
    const diffTime = Math.abs(this.closingDate.getTime() - this.openingDate.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
}
