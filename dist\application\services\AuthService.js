import { UserMapper } from '../mapppers/UserMapper.js';
import jwt from 'jsonwebtoken';
import { env } from '../../infrastructure/config/env.js';
export class AuthService {
    prisma;
    passwordHasher;
    auditService;
    constructor(prisma, passwordHasher, auditService) {
        this.prisma = prisma;
        this.passwordHasher = passwordHasher;
        this.auditService = auditService;
    }
    async login(loginDto) {
        // Get user by email with relations
        const prismaUser = await this.prisma.pTEIUser.findUnique({
            where: { email: loginDto.email },
            include: {
                Role: true,
                Department: true
            }
        });
        if (!prismaUser) {
            throw new Error('Invalid credentials');
        }
        const user = UserMapper.toDomain(prismaUser);
        if (user.isAccountLockedOut) {
            throw new Error('Account is locked due to too many failed login attempts.');
        }
        if (user.accountStatus?.toLowerCase() !== 'active') {
            throw new Error('Account is not active.');
        }
        const hashedInput = this.passwordHasher.hash(loginDto.password);
        if (user.hashedPassword !== hashedInput) {
            // Update failed login attempts
            const updatedUser = user.incrementFailedLoginAttempts();
            const updatedPrismaData = UserMapper.toPrisma(updatedUser);
            await this.prisma.pTEIUser.update({
                where: { UserId: user.userId },
                data: updatedPrismaData
            });
            await this.auditService.logAsync(user.userId, 'LOGIN_FAILED', 'PTEIUser', user.userId, null, null, 'Invalid login');
            throw new Error('Invalid credentials.');
        }
        // Reset failed login attempts and set logged in
        const loggedInUser = user.resetFailedLoginAttempts().setLoggedIn(true);
        const loggedInPrismaData = UserMapper.toPrisma(loggedInUser);
        await this.prisma.pTEIUser.update({
            where: { UserId: user.userId },
            data: loggedInPrismaData
        });
        await this.auditService.logAsync(user.userId, 'LOGIN_SUCCESS', 'PTEIUser', user.userId, null, null, 'User logged in');
        const token = this.generateJwtToken(user, prismaUser.Role?.RoleName);
        return {
            token,
            user: {
                userId: user.userId,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                roleId: user.roleId,
                roleName: prismaUser.Role?.RoleName,
                departmentId: user.departmentId,
                departmentName: prismaUser.Department?.DepartmentName,
                userType: user.userType.toString()
            }
        };
    }
    async logout(userId) {
        const prismaUser = await this.prisma.pTEIUser.findUnique({
            where: { UserId: userId }
        });
        if (!prismaUser) {
            throw new Error('User not found');
        }
        const user = UserMapper.toDomain(prismaUser);
        const loggedOutUser = user.setLoggedIn(false);
        const loggedOutPrismaData = UserMapper.toPrisma(loggedOutUser);
        await this.prisma.pTEIUser.update({
            where: { UserId: userId },
            data: loggedOutPrismaData
        });
        await this.auditService.logAsync(userId, 'LOGOUT', 'PTEIUser', userId, null, null, 'User logged out');
    }
    generateJwtToken(user, roleName) {
        if (!roleName) {
            throw new Error(`Role is not set for user ${user.email}.`);
        }
        const payload = {
            sub: user.userId,
            email: user.email,
            role: roleName,
            userType: user.userType.toString()
        };
        return jwt.sign(payload, env.JWT_SECRET, { expiresIn: '8h' });
    }
}
