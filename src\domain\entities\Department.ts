// Domain Entity for Department
export class Department {
  constructor(
    public readonly departmentId: string,
    public readonly departmentName: string,
    public readonly description?: string,
    public readonly isActive: boolean = true,
    public readonly createdAt: Date = new Date()
  ) {}

  // Domain methods
  public deactivate(): Department {
    return new Department(
      this.departmentId,
      this.departmentName,
      this.description,
      false,
      this.createdAt
    );
  }

  public activate(): Department {
    return new Department(
      this.departmentId,
      this.departmentName,
      this.description,
      true,
      this.createdAt
    );
  }

  public updateDetails(newName: string, newDescription?: string): Department {
    return new Department(
      this.departmentId,
      newName,
      newDescription,
      this.isActive,
      this.createdAt
    );
  }

  // Business rules
  public canBeDeleted(): boolean {
    return !this.isActive;
  }

  public isValidForAssignment(): boolean {
    return this.isActive && this.departmentName.trim().length > 0;
  }
}
