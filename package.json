{"name": "ptei_backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"@prisma/client": "^6.14.0", "@types/jsonwebtoken": "^9.0.10", "dotenv": "^17.2.1", "fastify": "^5.5.0", "fastify-plugin": "^5.0.1", "jsonwebtoken": "^9.0.2", "mssql": "^11.0.1"}, "devDependencies": {"@types/node": "^24.3.0", "pino-pretty": "^13.1.1", "prisma": "^6.14.0", "tsx": "^4.20.4", "typescript": "^5.9.2"}}