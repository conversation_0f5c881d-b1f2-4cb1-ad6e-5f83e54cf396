// Domain Entity for AuditLog
export class AuditLog {
    logId;
    actionType;
    tableName;
    recordId;
    ipAddress;
    sessionId;
    userAgent;
    userId;
    oldValues;
    newValues;
    actionTimestamp;
    additionalInfo;
    constructor(logId, actionType, tableName, recordId, ipAddress, sessionId, userAgent, userId, oldValues, newValues, actionTimestamp = new Date(), additionalInfo) {
        this.logId = logId;
        this.actionType = actionType;
        this.tableName = tableName;
        this.recordId = recordId;
        this.ipAddress = ipAddress;
        this.sessionId = sessionId;
        this.userAgent = userAgent;
        this.userId = userId;
        this.oldValues = oldValues;
        this.newValues = newValues;
        this.actionTimestamp = actionTimestamp;
        this.additionalInfo = additionalInfo;
    }
    // Domain methods
    static createForCreate(logId, tableName, recordId, newValues, userId, ipAddress, sessionId, userAgent, additionalInfo) {
        return new AuditLog(logId, 'CREATE', tableName, recordId, ipAddress, sessionId, userAgent, userId, undefined, newValues, new Date(), additionalInfo);
    }
    static createForUpdate(logId, tableName, recordId, oldValues, newValues, userId, ipAddress, sessionId, userAgent, additionalInfo) {
        return new AuditLog(logId, 'UPDATE', tableName, recordId, ipAddress, sessionId, userAgent, userId, oldValues, newValues, new Date(), additionalInfo);
    }
    static createForDelete(logId, tableName, recordId, oldValues, userId, ipAddress, sessionId, userAgent, additionalInfo) {
        return new AuditLog(logId, 'DELETE', tableName, recordId, ipAddress, sessionId, userAgent, userId, oldValues, undefined, new Date(), additionalInfo);
    }
    // Business rules
    isRecentAction(minutesThreshold = 30) {
        const now = new Date();
        const diffInMinutes = (now.getTime() - this.actionTimestamp.getTime()) / (1000 * 60);
        return diffInMinutes <= minutesThreshold;
    }
    isCriticalAction() {
        const criticalActions = ['DELETE', 'UPDATE_PASSWORD', 'LOCK_ACCOUNT', 'UNLOCK_ACCOUNT'];
        return criticalActions.includes(this.actionType);
    }
}
