// University Service
import { PrismaClient } from '../../generated/prisma/index.js';
import { University } from '../../domain/entities/index.js';
import { UniversityMapper } from '../mapppers/UniversityMapper.js';
import { UniversityDto, CreateUniversityDto, UpdateUniversityDto } from '../dto/university.dto.js';

export class UniversityService {
  constructor(private prisma: PrismaClient) {}

  async createUniversity(createDto: CreateUniversityDto, userId: string): Promise<UniversityDto> {
    // Check user permissions (simplified - in real app you'd check roles/permissions)
    const user = await this.prisma.pTEIUser.findUnique({
      where: { UserId: userId },
      include: { Role: true }
    });

    if (!user || !user.Role || user.Role.RoleName !== 'Admin') {
      throw new Error('Unauthorized access');
    }

    const university = new University(
      crypto.randomUUID(),
      createDto.universityName,
      createDto.primaryEmail,
      createDto.contactPerson,
      createDto.phone,
      true,
      new Date(),
      new Date(),
      createDto.ccEmails,
      createDto.bccEmails
    );

    const prismaData = UniversityMapper.toPrisma(university);
    const createdUniversity = await this.prisma.university.create({
      data: prismaData
    });

    return {
      universityId: createdUniversity.UniversityId,
      universityName: createdUniversity.UniversityName,
      primaryEmail: createdUniversity.PrimaryEmail,
      ccEmails: createdUniversity.CCEmails || undefined,
      bccEmails: createdUniversity.BCCEmails || undefined,
      contactPerson: createdUniversity.ContactPerson,
      phone: createdUniversity.Phone,
      isActive: createdUniversity.IsActive,
      createdAt: createdUniversity.CreatedAt,
      updatedAt: createdUniversity.UpdatedAt
    };
  }

  async updateUniversity(updateDto: UpdateUniversityDto, userId: string): Promise<UniversityDto> {
    // Check user permissions
    const user = await this.prisma.pTEIUser.findUnique({
      where: { UserId: userId },
      include: { Role: true }
    });

    if (!user || !user.Role || user.Role.RoleName !== 'Admin') {
      throw new Error('Unauthorized access');
    }

    const existingUniversity = await this.prisma.university.findUnique({
      where: { UniversityId: updateDto.universityId }
    });

    if (!existingUniversity) {
      throw new Error('University not found');
    }

    const updatedUniversity = await this.prisma.university.update({
      where: { UniversityId: updateDto.universityId },
      data: {
        UniversityName: updateDto.universityName ?? existingUniversity.UniversityName,
        PrimaryEmail: updateDto.primaryEmail ?? existingUniversity.PrimaryEmail,
        CCEmails: updateDto.ccEmails ?? existingUniversity.CCEmails,
        BCCEmails: updateDto.bccEmails ?? existingUniversity.BCCEmails,
        ContactPerson: updateDto.contactPerson ?? existingUniversity.ContactPerson,
        Phone: updateDto.phone ?? existingUniversity.Phone,
        UpdatedAt: new Date()
      }
    });

    return {
      universityId: updatedUniversity.UniversityId,
      universityName: updatedUniversity.UniversityName,
      primaryEmail: updatedUniversity.PrimaryEmail,
      ccEmails: updatedUniversity.CCEmails || undefined,
      bccEmails: updatedUniversity.BCCEmails || undefined,
      contactPerson: updatedUniversity.ContactPerson,
      phone: updatedUniversity.Phone,
      isActive: updatedUniversity.IsActive,
      createdAt: updatedUniversity.CreatedAt,
      updatedAt: updatedUniversity.UpdatedAt
    };
  }

  async getUniversityById(id: string): Promise<UniversityDto | null> {
    const prismaUniversity = await this.prisma.university.findUnique({
      where: { UniversityId: id }
    });

    if (!prismaUniversity) {
      return null;
    }

    return {
      universityId: prismaUniversity.UniversityId,
      universityName: prismaUniversity.UniversityName,
      primaryEmail: prismaUniversity.PrimaryEmail,
      ccEmails: prismaUniversity.CCEmails || undefined,
      bccEmails: prismaUniversity.BCCEmails || undefined,
      contactPerson: prismaUniversity.ContactPerson,
      phone: prismaUniversity.Phone,
      isActive: prismaUniversity.IsActive,
      createdAt: prismaUniversity.CreatedAt,
      updatedAt: prismaUniversity.UpdatedAt
    };
  }

  async getAllUniversities(): Promise<UniversityDto[]> {
    const prismaUniversities = await this.prisma.university.findMany({
      where: { IsActive: true },
      orderBy: { UniversityName: 'asc' }
    });

    return prismaUniversities.map(uni => ({
      universityId: uni.UniversityId,
      universityName: uni.UniversityName,
      primaryEmail: uni.PrimaryEmail,
      ccEmails: uni.CCEmails || undefined,
      bccEmails: uni.BCCEmails || undefined,
      contactPerson: uni.ContactPerson,
      phone: uni.Phone,
      isActive: uni.IsActive,
      createdAt: uni.CreatedAt,
      updatedAt: uni.UpdatedAt
    }));
  }

  async deleteUniversity(id: string, userId: string): Promise<boolean> {
    // Check user permissions
    const user = await this.prisma.pTEIUser.findUnique({
      where: { UserId: userId },
      include: { Role: true }
    });

    if (!user || !user.Role || user.Role.RoleName !== 'Admin') {
      return false;
    }

    const university = await this.prisma.university.findUnique({
      where: { UniversityId: id }
    });

    if (!university) {
      return false;
    }

    // Soft delete by setting IsActive to false
    await this.prisma.university.update({
      where: { UniversityId: id },
      data: { IsActive: false, UpdatedAt: new Date() }
    });

    return true;
  }
}
