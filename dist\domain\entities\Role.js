// Domain Entity for Role
export class Role {
    roleId;
    roleName;
    description;
    isActive;
    createdAt;
    constructor(roleId, roleName, description, isActive = true, createdAt = new Date()) {
        this.roleId = roleId;
        this.roleName = roleName;
        this.description = description;
        this.isActive = isActive;
        this.createdAt = createdAt;
    }
    // Domain methods
    deactivate() {
        return new Role(this.roleId, this.roleName, this.description, false, this.createdAt);
    }
    activate() {
        return new Role(this.roleId, this.roleName, this.description, true, this.createdAt);
    }
    updateDescription(newDescription) {
        return new Role(this.roleId, this.roleName, newDescription, this.isActive, this.createdAt);
    }
    // Business rules
    canBeDeleted() {
        return !this.isActive;
    }
    isValidForAssignment() {
        return this.isActive && this.roleName.trim().length > 0;
    }
}
